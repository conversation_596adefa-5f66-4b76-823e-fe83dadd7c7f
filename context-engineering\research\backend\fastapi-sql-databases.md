# FastAPI - SQL (Relational) Databases

This document is a summary of the official FastAPI documentation on how to use SQL (relational) databases with FastAPI.
The original documentation can be found at https://fastapi.tiangolo.com/tutorial/sql-databases/.

## Overview

FastAPI does not require a specific database. You can use any database you want. This tutorial shows an example using SQLModel, which is built on top of SQLAlchemy and Pydantic.

### Supported Databases

Because SQLModel is based on SQLAlchemy, you can use any database supported by SQLAlchemy, including:

*   PostgreSQL
*   MySQL
*   SQLite
*   Oracle
*   Microsoft SQL Server

This tutorial uses SQLite for simplicity.

## Using SQLModel

### Installation

First, install `sqlmodel`:

```bash
pip install sqlmodel
```

### Creating Models

You can create database models that are also Pydantic models.

```python
from typing import Optional

from sqlmodel import Field, SQLModel


class Hero(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(index=True)
    secret_name: str
    age: Optional[int] = Field(default=None, index=True)
```

### Creating an Engine

The engine is what connects to the database.

```python
from sqlmodel import create_engine

sqlite_file_name = "database.db"
sqlite_url = f"sqlite:///{sqlite_file_name}"

connect_args = {"check_same_thread": False}
engine = create_engine(sqlite_url, echo=True, connect_args=connect_args)
```

### Creating Tables

You can create the database tables from your models.

```python
from sqlmodel import SQLModel


def create_db_and_tables():
    SQLModel.metadata.create_all(engine)
```

### Creating a Session Dependency

A session is used to interact with the database. You can create a dependency to get a session for each request.

```python
from sqlmodel import Session


def get_session():
    with Session(engine) as session:
        yield session
```

### Application Startup

You can create the database and tables when the application starts.

```python
from fastapi import FastAPI


app = FastAPI()


@app.on_event("startup")
def on_startup():
    create_db_and_tables()
```

### CRUD Operations

You can perform Create, Read, Update, and Delete (CRUD) operations using the session.

#### Create

```python
from fastapi import Depends, FastAPI
from sqlmodel import Session

from .database import get_session
from .models import Hero, HeroCreate


@app.post("/heroes/", response_model=Hero)
def create_hero(hero: HeroCreate, session: Session = Depends(get_session)):
    db_hero = Hero.from_orm(hero)
    session.add(db_hero)
    session.commit()
    session.refresh(db_hero)
    return db_hero
```

#### Read

```python
from typing import List

from fastapi import Depends, FastAPI, Query
from sqlmodel import Session, select

from .database import get_session
from .models import Hero


@app.get("/heroes/", response_model=List[Hero])
def read_heroes(
    *,
    session: Session = Depends(get_session),
    offset: int = 0,
    limit: int = Query(default=100, lte=100),
):
    heroes = session.exec(select(Hero).offset(offset).limit(limit)).all()
    return heroes
```

## Multiple Models

You can use multiple models to control what data is exposed in your API.

*   `HeroBase`: Shared properties.
*   `Hero`: The database model, including all fields.
*   `HeroCreate`: The model for creating a new hero (e.g., without the `id`).
*   `HeroPublic`: The model for what is returned to the client (e.g., without the `secret_name`).
*   `HeroUpdate`: The model for updating a hero, with all fields optional.

This allows you to have different models for input and output, and for different operations, which is a powerful feature for building secure and robust APIs.
