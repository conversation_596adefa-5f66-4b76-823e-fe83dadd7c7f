# Firecrawl API - Detailed Technical Reference

**Research Date**: July 8, 2025  
**Source**: https://docs.firecrawl.dev/api-reference/  
**Status**: Active Research - Phase 2 of 5

## API Technical Specifications

### Base Configuration
- **Base URL**: `https://api.firecrawl.dev`
- **Authentication**: Bearer token (`Authorization: Bearer fc-YOUR_API_KEY`)
- **Content Type**: `application/json`
- **Rate Limiting**: Applied to all endpoints with 429 response on exceed

### HTTP Status Codes
| Status | Meaning | Action Required |
|--------|---------|-----------------|
| 200 | Success | Continue processing |
| 400 | Bad parameters | Validate request format |
| 401 | Missing API key | Check authentication |
| 402 | Payment required | Check billing/credits |
| 404 | Resource not found | Verify URL/endpoint |
| 429 | Rate limit exceeded | Implement backoff strategy |
| 5xx | Server error | Retry with exponential backoff |

## Scrape Endpoint - Detailed Analysis

### Endpoint: `POST /v1/scrape`

#### Required Parameters
```json
{
  "url": "string" // The URL to scrape - REQUIRED
}
```

#### Core Configuration Options
```json
{
  "onlyMainContent": true,        // Default: true - Extract main content only
  "maxAge": 0,                    // Cache age in ms - 0 disables caching
  "timeout": 30000,               // Default: 30s timeout
  "waitFor": 0,                   // Delay before scraping (ms)
  "mobile": false,                // Emulate mobile device
  "skipTlsVerification": false,   // Skip SSL verification
  "parsePDF": true,               // Extract PDF content vs base64
  "blockAds": true,               // Default: true - Block ads/popups
  "removeBase64Images": true,     // Remove large base64 images
  "storeInCache": true,           // Store in Firecrawl cache
  "zeroDataRetention": false      // Enterprise privacy feature
}
```

#### Content Filtering
```json
{
  "includeTags": ["article", "main", "p"],  // Only include these HTML tags
  "excludeTags": ["nav", "footer", "ads"]   // Exclude these HTML tags
}
```

#### Output Formats
```json
{
  "formats": [
    "markdown",      // Clean markdown (default)
    "html",          // Cleaned HTML
    "rawHtml",       // Original HTML
    "screenshot",    // Page screenshot
    "links"          // Extracted links
  ]
}
```

#### Proxy Configuration
```json
{
  "proxy": "basic" | "stealth" | "auto"
}
```
- **basic**: Fast, works for basic sites
- **stealth**: Advanced anti-bot bypass (costs 5 credits)
- **auto**: Automatic retry with stealth if basic fails

#### Location Settings
```json
{
  "location": {
    "country": "US",           // Country code for proxy
    "languages": ["en-US"]     // Language preferences
  }
}
```

#### Headers and Authentication
```json
{
  "headers": {
    "User-Agent": "custom-agent",
    "Cookie": "session=xyz",
    "Authorization": "Bearer token"
  }
}
```

### JSON Extraction Options
```json
{
  "jsonOptions": {
    "schema": {},              // Pydantic-style schema
    "systemPrompt": "string",  // AI system instructions
    "prompt": "string"         // Extraction prompt
  }
}
```

### Actions System
Complex page interactions before scraping:
```json
{
  "actions": [
    {"type": "wait", "milliseconds": 2000},
    {"type": "click", "selector": ".button"},
    {"type": "write", "text": "search term"},
    {"type": "press", "key": "Enter"},
    {"type": "scroll", "direction": "down"},
    {"type": "screenshot"},
    {"type": "scrape"}
  ]
}
```

### Response Structure
```json
{
  "success": true,
  "data": {
    "markdown": "string",      // Clean markdown content
    "html": "string",          // Cleaned HTML
    "rawHtml": "string",       // Original HTML
    "screenshot": "string",    // Base64 screenshot
    "links": ["string"],       // Extracted URLs
    "metadata": {
      "title": "string",
      "description": "string",
      "language": "string",
      "sourceURL": "string",
      "statusCode": 200,
      "error": "string"
    },
    "llm_extraction": {},      // Structured extraction results
    "actions": {
      "screenshots": ["string"],
      "scrapes": [{"url": "string", "html": "string"}],
      "javascriptReturns": [{"type": "string", "value": "any"}]
    },
    "warning": "string"
  }
}
```

## Student Helper Integration Analysis

### Performance Optimizations
1. **Caching Strategy**: Use `maxAge` parameter for frequently accessed academic sources
2. **Content Filtering**: Use `onlyMainContent: true` and `excludeTags` to reduce noise
3. **Format Selection**: Use `markdown` format for LLM processing efficiency
4. **Proxy Selection**: Use `auto` proxy for reliability with unknown sites

### Academic Use Case Patterns

#### Research Paper Extraction
```json
{
  "url": "academic-paper-url",
  "formats": ["markdown", "html"],
  "onlyMainContent": true,
  "excludeTags": ["nav", "footer", "aside", "ads"],
  "jsonOptions": {
    "prompt": "Extract title, authors, abstract, publication date, DOI, and key findings"
  }
}
```

#### Citation Data Extraction
```json
{
  "url": "source-url",
  "formats": ["markdown"],
  "jsonOptions": {
    "schema": {
      "type": "object",
      "properties": {
        "title": {"type": "string"},
        "authors": {"type": "array", "items": {"type": "string"}},
        "publication_date": {"type": "string"},
        "doi": {"type": "string"},
        "journal": {"type": "string"}
      }
    }
  }
}
```

#### Paywalled Content Access
```json
{
  "url": "paywalled-url",
  "headers": {
    "Cookie": "institutional-access-cookie"
  },
  "actions": [
    {"type": "wait", "milliseconds": 3000},
    {"type": "click", "selector": ".access-button"},
    {"type": "wait", "milliseconds": 2000},
    {"type": "scrape"}
  ]
}
```

### Error Handling Strategy
```python
def handle_firecrawl_response(response):
    if response.status_code == 429:
        # Rate limit - implement exponential backoff
        return "RATE_LIMITED"
    elif response.status_code == 402:
        # Credits exhausted - switch to fallback API
        return "CREDITS_EXHAUSTED"
    elif response.status_code == 400:
        # Bad request - validate input
        return "INVALID_REQUEST"
    elif 500 <= response.status_code < 600:
        # Server error - retry with backoff
        return "SERVER_ERROR"
    else:
        return response.json()
```

### Cost Optimization
1. **Credit Management**: Monitor credit usage per request
2. **Caching**: Implement aggressive caching for repeated sources
3. **Format Selection**: Only request needed formats
4. **Proxy Strategy**: Use basic proxy unless stealth required

### Quality Assurance
1. **Content Validation**: Check metadata.statusCode for success
2. **Content Length**: Validate meaningful content extracted
3. **Source Verification**: Use metadata for source credibility scoring
4. **Error Recovery**: Implement fallback strategies for failed scrapes

## Integration Architecture Recommendations

### API Client Design
```python
class FirecrawlClient:
    def __init__(self, api_key: str, default_options: dict = None):
        self.api_key = api_key
        self.base_url = "https://api.firecrawl.dev/v1"
        self.default_options = default_options or {}
        
    def scrape_academic_source(self, url: str, extraction_schema: dict = None):
        # Optimized for academic content
        pass
        
    def extract_citation_data(self, url: str):
        # Specialized citation extraction
        pass
        
    def scrape_with_auth(self, url: str, auth_headers: dict):
        # Handle institutional access
        pass
```

### Quality Metrics
- Success rate: >95% for accessible academic sources
- Response time: <10 seconds for single page scrapes
- Content quality: Markdown cleanliness score >90%
- Credit efficiency: <2 credits per successful scrape

## Next Research Steps
1. Crawl and Map endpoint analysis
2. Extract endpoint detailed testing
3. Pricing structure analysis
4. Performance benchmarking
5. Error rate analysis with academic sites

## Research Progress
- [x] Core API overview
- [x] Scrape endpoint detailed analysis
- [ ] Crawl endpoint analysis
- [ ] Extract endpoint analysis
- [ ] Pricing and rate limit analysis
- [ ] Performance benchmarks
