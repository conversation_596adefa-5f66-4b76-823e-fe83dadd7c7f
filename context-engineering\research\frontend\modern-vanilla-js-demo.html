<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline';
        style-src 'self' 'unsafe-inline';
        img-src 'self' data: https:;
        connect-src 'self' https://jsonplaceholder.typicode.com;
    ">
    <title>Modern Vanilla JavaScript App 2025</title>
    
    <style>
        /* Modern CSS with CSS Grid and Flexbox */
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --background-color: #ffffff;
            --surface-color: #f9fafb;
            --text-color: #111827;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 0.5rem;
            --transition: all 0.2s ease-in-out;
        }

        [data-theme="dark"] {
            --background-color: #0f172a;
            --surface-color: #1e293b;
            --text-color: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: var(--transition);
        }

        .app-container {
            min-height: 100vh;
            display: grid;
            grid-template-rows: auto 1fr auto;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Header Styles */
        .app-header {
            background: var(--surface-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .app-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .theme-toggle {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.875rem;
        }

        .theme-toggle:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        /* Todo App Styles */
        .todo-container {
            background: var(--surface-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .todo-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .todo-header h2 {
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.875rem;
        }

        .filter-btn:hover {
            background: var(--border-color);
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .todo-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .todo-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            opacity: 0;
            transform: translateY(10px);
        }

        .todo-item.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .todo-item:hover {
            background: var(--background-color);
        }

        .todo-item.completed {
            opacity: 0.6;
        }

        .todo-item.completed .todo-text {
            text-decoration: line-through;
            color: var(--text-secondary);
        }

        .todo-checkbox {
            width: 1.25rem;
            height: 1.25rem;
            cursor: pointer;
        }

        .todo-text {
            flex: 1;
            font-size: 1rem;
        }

        .edit-input {
            flex: 1;
            border: 1px solid var(--primary-color);
            border-radius: 0.25rem;
            padding: 0.25rem 0.5rem;
            font-size: 1rem;
            background: var(--background-color);
            color: var(--text-color);
        }

        .todo-actions {
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: var(--transition);
        }

        .todo-item:hover .todo-actions {
            opacity: 1;
        }

        .edit-btn, .delete-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: var(--transition);
        }

        .edit-btn:hover, .delete-btn:hover {
            background: var(--border-color);
        }

        .todo-input {
            display: flex;
            gap: 0.5rem;
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .new-todo-input {
            flex: 1;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            font-size: 1rem;
            background: var(--background-color);
            color: var(--text-color);
            transition: var(--transition);
        }

        .new-todo-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .add-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
        }

        .add-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* Footer Styles */
        .app-footer {
            text-align: center;
            padding: 2rem 1rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .shortcuts-info {
            margin-top: 1rem;
            padding: 1rem;
            background: var(--surface-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .shortcut {
            display: inline-block;
            background: var(--border-color);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.75rem;
            margin: 0 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .app-container {
                padding: 0.5rem;
            }

            .app-header {
                flex-direction: column;
                text-align: center;
            }

            .filter-buttons {
                justify-content: center;
            }

            .todo-input {
                flex-direction: column;
            }

            .todo-item {
                padding: 0.75rem;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .todo-item.visible {
            animation: fadeIn 0.3s ease-out;
        }

        /* Focus and accessibility */
        .filter-btn:focus,
        .add-btn:focus,
        .new-todo-input:focus,
        .theme-toggle:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Loading state */
        .loading::after {
            content: '';
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Performance optimizations */
        .todo-item {
            contain: layout style paint;
        }

        .todo-list {
            will-change: scroll-position;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1 class="app-title">Modern Vanilla JS Todo App 2025</h1>
            <div>
                <button class="theme-toggle" id="theme-toggle">
                    🌙 Toggle Theme
                </button>
            </div>
        </header>

        <main>
            <div id="todo-app"></div>
        </main>

        <footer class="app-footer">
            <p>Built with Vanilla JavaScript 2025 • ES2024/ES2025 Features</p>
            <div class="shortcuts-info">
                <strong>Keyboard Shortcuts:</strong>
                <span class="shortcut">Ctrl/Cmd + K</span> Search
                <span class="shortcut">Ctrl/Cmd + /</span> Help
                <span class="shortcut">Enter</span> Add Todo
                <span class="shortcut">Escape</span> Cancel Edit
            </div>
        </footer>
    </div>

    <!-- Load the modern JavaScript app -->
    <script type="module">
        // Import the main app components
        import { App, SecureApiClient, TodoList } from './modern-vanilla-js-template.js';

        // Mock API for demonstration (replace with real API)
        class MockApiClient extends SecureApiClient {
            constructor() {
                super('https://jsonplaceholder.typicode.com');
                this.todos = [
                    { id: 1, text: 'Learn ES2025 features', completed: false },
                    { id: 2, text: 'Build modern vanilla JS app', completed: true },
                    { id: 3, text: 'Implement security best practices', completed: false },
                    { id: 4, text: 'Add performance monitoring', completed: false },
                    { id: 5, text: 'Test accessibility features', completed: false }
                ];
                this.nextId = 6;
            }

            async get(endpoint) {
                // Simulate network delay
                await new Promise(resolve => setTimeout(resolve, 300));
                
                if (endpoint === '/todos') {
                    return [...this.todos];
                }
                if (endpoint === '/user/settings') {
                    return {
                        theme: localStorage.getItem('theme') || 'light',
                        notifications: true
                    };
                }
                return [];
            }

            async post(endpoint, data) {
                await new Promise(resolve => setTimeout(resolve, 200));
                
                if (endpoint === '/todos') {
                    const newTodo = { ...data, id: this.nextId++ };
                    this.todos.push(newTodo);
                    return newTodo;
                }
                return null;
            }

            async put(endpoint, data) {
                await new Promise(resolve => setTimeout(resolve, 200));
                
                const match = endpoint.match(/\/todos\/(\d+)/);
                if (match) {
                    const id = parseInt(match[1]);
                    const index = this.todos.findIndex(t => t.id === id);
                    if (index !== -1) {
                        this.todos[index] = { ...this.todos[index], ...data };
                        return this.todos[index];
                    }
                }
                return null;
            }

            async delete(endpoint) {
                await new Promise(resolve => setTimeout(resolve, 200));
                
                const match = endpoint.match(/\/todos\/(\d+)/);
                if (match) {
                    const id = parseInt(match[1]);
                    const index = this.todos.findIndex(t => t.id === id);
                    if (index !== -1) {
                        this.todos.splice(index, 1);
                        return true;
                    }
                }
                return false;
            }
        }

        // Enhanced App class for demo
        class DemoApp extends App {
            constructor() {
                super();
                // Override with mock API for demo
                this._api = new MockApiClient();
            }

            get api() {
                return this._api;
            }

            initializeComponents() {
                // Initialize TodoList component with mock API
                const todoContainer = document.querySelector('#todo-app');
                if (todoContainer) {
                    const todoList = new TodoList('#todo-app', this._api);
                    this._components.set('todoList', todoList);
                }

                // Initialize theme toggle
                this.initializeThemeToggle();
            }

            initializeThemeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    themeToggle.addEventListener('click', () => {
                        const currentTheme = document.documentElement.getAttribute('data-theme');
                        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                        
                        document.documentElement.setAttribute('data-theme', newTheme);
                        localStorage.setItem('theme', newTheme);
                        
                        // Update button text
                        themeToggle.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
                    });

                    // Initialize theme from localStorage
                    const savedTheme = localStorage.getItem('theme') || 'light';
                    document.documentElement.setAttribute('data-theme', savedTheme);
                    themeToggle.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
                }
            }

            setupGlobalEventListeners() {
                super.setupGlobalEventListeners();

                // Add demo-specific event listeners
                document.addEventListener('keydown', (event) => {
                    // Focus on todo input when typing starts
                    if (event.key.length === 1 && !event.ctrlKey && !event.metaKey && !event.altKey) {
                        const activeElement = document.activeElement;
                        if (activeElement === document.body || activeElement === document.documentElement) {
                            const todoInput = document.querySelector('.new-todo-input');
                            if (todoInput) {
                                todoInput.focus();
                                // Don't prevent default to allow the character to be typed
                            }
                        }
                    }
                });
            }

            setupPerformanceMonitoring() {
                super.setupPerformanceMonitoring();

                // Add demo-specific performance monitoring
                if ('requestIdleCallback' in window) {
                    requestIdleCallback(() => {
                        console.log('App initialized during idle time');
                    });
                }
            }
        }

        // Initialize the demo app
        const demoApp = new DemoApp();
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => demoApp.init());
        } else {
            demoApp.init();
        }

        // Make app available globally for debugging
        window.demoApp = demoApp;

        // Service Worker registration (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                // Uncomment when you have a service worker
                // navigator.serviceWorker.register('/sw.js')
                //     .then(registration => console.log('SW registered'))
                //     .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
