# FastAPI: Background Tasks

## Summary

FastAPI provides a simple way to run tasks in the background after a response has been sent to the client. This is useful for operations that don't need to block the client's response, such as sending email notifications or processing data.

## Key Concepts

### Using `BackgroundTasks`

- Import `BackgroundTasks` from `fastapi`.
- Add a parameter of type `BackgroundTasks` to your path operation function. FastAPI will automatically create and inject an instance.
- Use the `add_task()` method on the `BackgroundTasks` object to add a function to be executed in the background.

### Task Functions

- Can be standard `def` or `async def` functions.
- Can accept arguments, which are passed to them via `add_task()`.

### Dependency Injection

- `BackgroundTasks` can be used with the dependency injection system. You can declare it as a dependency in path operations, sub-dependencies, etc.
- FastAPI reuses the same `BackgroundTasks` object, so all tasks are collected and run after the response.

### Caveats

- For heavy, long-running background computations, especially those that don't need to share memory with the main application, using a more robust tool like Celery with a message queue (e.g., RabbitMQ, Redis) is recommended. This allows for running tasks in separate processes or even on different servers.
- `BackgroundTasks` is ideal for smaller, simpler tasks like sending notifications or logging.

## Code Examples

### Basic Usage

```python
from fastapi import BackgroundTasks, FastAPI

app = FastAPI()

def write_notification(email: str, message=""):
    with open("log.txt", mode="w") as email_file:
        content = f"notification for {email}: {message}"
        email_file.write(content)

@app.post("/send-notification/{email}")
async def send_notification(email: str, background_tasks: BackgroundTasks):
    background_tasks.add_task(write_notification, email, message="some notification")
    return {"message": "Notification sent in the background"}
```

### Using with Dependency Injection

```python
from typing import Annotated
from fastapi import BackgroundTasks, Depends, FastAPI

app = FastAPI()

def write_log(message: str):
    with open("log.txt", mode="a") as log:
        log.write(message)

def get_query(background_tasks: BackgroundTasks, q: str | None = None):
    if q:
        message = f"found query: {q}\n"
        background_tasks.add_task(write_log, message)
    return q

@app.post("/send-notification/{email}")
async def send_notification(
    email: str,
    background_tasks: BackgroundTasks,
    q: Annotated[str, Depends(get_query)],
):
    message = f"message to {email}\n"
    background_tasks.add_task(write_log, message)
    return {"message": "Message sent"}
```
