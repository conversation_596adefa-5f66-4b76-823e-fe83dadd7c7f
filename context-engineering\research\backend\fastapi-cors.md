# FastAPI: CORS (Cross-Origin Resource Sharing)

## Summary

CORS (Cross-Origin Resource Sharing) is a browser security feature that restricts how a web page from one origin can request resources from another origin. FastAPI provides a `CORSMiddleware` to handle the necessary HTTP headers to allow or deny cross-origin requests.

## Key Concepts

### Origin

An "origin" is defined by the combination of the protocol (e.g., `http`, `https`), domain (e.g., `example.com`, `localhost`), and port (e.g., `80`, `8080`). If any of these three parts differ between the frontend and the backend, the request is considered "cross-origin."

### How CORS Works

1.  **Preflight Request:** For requests that can modify data (e.g., `POST`, `PUT`, `DELETE`) or have certain headers, the browser first sends an `OPTIONS` request to the server. This is called a "preflight" request.
2.  **Server Response:** The server responds to the preflight request with headers indicating which origins, methods, and headers are allowed.
3.  **Actual Request:** If the preflight response is permissive, the browser proceeds to send the actual request. For simple requests (like `GET` or `HEAD` without custom headers), the browser sends the request directly and checks the CORS headers on the response.

### `CORSMiddleware`

FastAPI's `CORSMiddleware` simplifies the process of adding the correct CORS headers to responses. It is added to the application using `app.add_middleware()`.

#### Key Parameters:

-   `allow_origins`: A list of strings specifying which origins are allowed to make cross-origin requests. A wildcard `"*"` can be used, but it has security implications and is not allowed if `allow_credentials` is `True`.
-   `allow_credentials`: A boolean indicating if cookies and authorization headers should be supported for cross-origin requests. Defaults to `False`.
-   `allow_methods`: A list of HTTP methods that are allowed (e.g., `["GET", "POST"]`). A wildcard `"*"` allows all standard methods.
-   `allow_headers`: A list of HTTP headers that are allowed. A wildcard `"*"` allows all headers.
-   `expose_headers`: A list of response headers that should be accessible to the browser.
-   `max_age`: The maximum time in seconds for browsers to cache CORS responses.

## Code Example

This example configures a FastAPI application to allow cross-origin requests from a specific list of origins, allowing all methods and headers, and supporting credentials.

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

# List of allowed origins
origins = [
    "http://localhost",
    "http://localhost:8080",
    "http://localhost.tiangolo.com",
    "https://localhost.tiangolo.com",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def main():
    return {"message": "Hello World"}
```
