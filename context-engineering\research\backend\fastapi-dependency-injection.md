Here is some relevant context from the web page https://fastapi.tiangolo.com/tutorial/dependencies/:
...
# Dependencies¶
...
## What is "Dependency Injection"¶


"Dependency Injection" means, in programming, that there is a way for your code (in this case, your path operation functions) to declare things that it requires to work and use: "dependencies".


And then, that system (in this case FastAPI) will take care of doing whatever is needed to provide your code with those
needed dependencies ("inject" the dependencies).


This is very useful when you need to:


• Have shared logic (the same code logic again and again).
• Share database connections.
• Enforce security, authentication, role requirements, etc.
• And many other things...


All these, while minimizing code repetition.


...
...
# Dependencies¶
...
## First Steps¶
...
### Create a dependency, or "dependable"¶


Let's first focus on the dependency.


It is just a function that can take all the same parameters that a path operation function can take:

  Python 3.10+ 
```
 from typing import Annotated
 
 from fastapi import Depends, FastAPI
 
 app = FastAPI()
 
 
 async def common_parameters(q: str | None = None, skip: int = 0, limit: int = 100):
     return {"q": q, "skip": skip, "limit": limit}
 
 
 @app.get("/items/")
 async def read_items(commons: Annotated[dict, Depends(common_parameters)]):
     return commons
 
 
 @app.get("/users/")
 async def read_users(commons: Annotated[dict, Depends(common_parameters)]):
     return commons

```
  🤓 Other versions and variants
That's it.


2 lines.


And it has the same shape and structure that all your path operation functions have.
...
...
# Dependencies¶
...
## First Steps¶
...
### Create a dependency, or "dependable"¶
...
You can think of it as a path operation function without the "decorator" (without the `@app.get("/some-path")`).


And it can return anything you want.


In this case, this dependency expects:


• An optional query parameter `q` that is a `str`.
• An optional query parameter `skip` that is an `int`, and by default is `0`.
• An optional query parameter `limit` that is an `int`, and by default is `100`.


And then it just returns a `dict` containing those values.


Info


FastAPI added support for `Annotated` (and started recommending it) in version 0.95.0.


If you have an older version, you would get errors when trying to use `Annotated`.


Make sure you [Upgrade the FastAPI version ↪](https://fastapi.tiangolo.com/deployment/versions/#upgrading-the-fastapi-versions) to at least 0.95.1 before using `Annotated`.
...
...
# Dependencies¶
...
## First Steps¶
...
### Import `Depends`¶

  Python 3.10+ 
```
 from typing import Annotated
 
 from fastapi import Depends, FastAPI
 
 app = FastAPI()
 
 
 async def common_parameters(q: str | None = None, skip: int = 0, limit: int = 100):
     return {"q": q, "skip": skip, "limit": limit}
 
 
 @app.get("/items/")
 async def read_items(commons: Annotated[dict, Depends(common_parameters)]):
     return commons
 
 
 @app.get("/users/")
 async def read_users(commons: Annotated[dict, Depends(common_parameters)]):
     return commons

```
  🤓 Other versions and variants
...
...
# Dependencies¶
...
## First Steps¶
...
### Declare the dependency, in the "dependant"¶


The same way you use `Body`, `Query`, etc. with your path operation function parameters, use `Depends` with a new parameter:

  Python 3.10+ 
```
 from typing import Annotated
 
 from fastapi import Depends, FastAPI
 
 app = FastAPI()
 
 
 async def common_parameters(q: str | None = None, skip: int = 0, limit: int = 100):
     return {"q": q, "skip": skip, "limit": limit}
 
 
 @app.get("/items/")
 async def read_items(commons: Annotated[dict, Depends(common_parameters)]):
     return commons
 
 
 @app.get("/users/")
 async def read_users(commons: Annotated[dict, Depends(common_parameters)]):
     return commons

```
  🤓 Other versions and variants
Although you use `Depends` in the parameters of your function the same way you use `Body`, `Query`, etc, `Depends` works a bit differently.
...
...
# Dependencies¶
...
## First Steps¶
...
### Declare the dependency, in the "dependant"¶
...
You only give `Depends` a single parameter.


This parameter must be something like a function.


You don't call it directly (don't add the parenthesis at the end), you just pass it as a
parameter to `Depends()`.


And that function takes parameters in the same way that path operation functions do.


Tip


You'll see what other "things", apart from functions, can be used as
dependencies in the next chapter.


Whenever a new request arrives, FastAPI will take care of:


• Calling your dependency ("dependable") function with the correct parameters.
• Get the result from your function.
• Assign that result to the parameter in your path operation function.

    common_parameters /items/ /users/
This way you write shared code once and FastAPI takes care of calling it for your path operations.


Check


Notice that you don't have to create a special class and pass it somewhere to FastAPI to "register" it or anything similar.
...
...
# Dependencies¶
...
## First Steps¶
...
### Declare the dependency, in the "dependant"¶
...
You just pass it to `Depends` and FastAPI knows how to do the rest.
...
...
# Dependencies¶
...
## First Steps¶


Let's see a very simple example. It will be so simple that it is not very
useful, for now.


But this way we can focus on how the Dependency Injection system works.


### Create a dependency, or "dependable"¶
...
### Import `Depends`¶
...
### Declare the dependency, in the "dependant"¶
...
...
# Dependencies¶
...
## Share `Annotated` dependencies¶


In the examples above, you see that there's a tiny bit of code duplication.


When you need to use the `common_parameters()` dependency, you have to write the whole parameter with the type annotation and `Depends()`:

 
```
 commons: Annotated[dict, Depends(common_parameters)]

```

But because we are using `Annotated`, we can store that `Annotated` value in a variable and use it in multiple places:

  Python 3.10+ 
```
 from typing import Annotated
 
 from fastapi import Depends, FastAPI
 
 app = FastAPI()
 
 
 async def common_parameters(q: str | None = None, skip: int = 0, limit: int = 100):
     return {"q": q, "skip": skip, "limit": limit}
 
 
 CommonsDep = Annotated[dict, Depends(common_parameters)]
 
 
 @app.get("/items/")
 async def read_items(commons: CommonsDep):
     return commons
 
 
 @app.get("/users/")
 async def read_users(commons: CommonsDep):
     return commons

```
...
...
# Dependencies¶
...
## Share `Annotated` dependencies¶
...
  🤓 Other versions and variants
Tip


This is just standard Python, it's called a "type alias", it's actually not
specific to FastAPI.


But because FastAPI is based on the Python standards, including `Annotated`, you can use this trick in your code. 😎


The dependencies will keep working as expected, and the best part is that the type information will be preserved, which means that your editor will be able to keep providing you with autocompletion, inline errors, etc. The same for other tools like `mypy`.


This will be especially useful when you use it in a large code base where you use the same dependencies over and over again in many path operations.
...
...
# Dependencies¶
...
## To `async` or not to `async`¶


As dependencies will also be called by FastAPI (the same as your path operation functions), the same rules apply while defining your functions.


You can use `async def` or normal `def`.


And you can declare dependencies with `async def` inside of normal `def`path operation functions, or `def` dependencies inside of `async def`path operation functions, etc.


It doesn't matter. FastAPI will know what to do.


Note


If you don't know, check the [Async: "In a hurry?" ↪](https://fastapi.tiangolo.com/async/#in-a-hurry) section about `async` and `await` in the docs.


## Integrated with OpenAPI¶


All the request declarations, validations and requirements of your dependencies
(and sub-dependencies) will be integrated in the same OpenAPI schema.


So, the interactive docs will have all the information from these dependencies
too:


![Image](https://fastapi.tiangolo.com/img/tutorial/dependencies/image01.png)




...
...
# Dependencies¶
...
## Simple usage¶


If you look at it, path operation functions are declared to be used whenever a path and operation matches, and then FastAPI takes care of calling the function with the correct parameters, extracting the
data from the request.


Actually, all (or most) of the web frameworks work in this same way.


You never call those functions directly. They are called by your framework (in
this case, FastAPI).


With the Dependency Injection system, you can also tell FastAPI that your path operation function also "depends" on something else that should be executed before your path operation function, and FastAPI will take care of executing it and "injecting" the results.


Other common terms for this same idea of "dependency injection" are:


• resources
• providers
• services
• injectables
• components


...
...
# Dependencies¶
...
## FastAPI plug-ins¶


Integrations and "plug-ins" can be built using the Dependency Injection system. But in fact, there is actually no need to create "plug-ins", as by using dependencies it's possible to declare an infinite number of
integrations and interactions that become available to your path operation functions.


And dependencies can be created in a very simple and intuitive way that allows
you to just import the Python packages you need, and integrate them with your API
functions in a couple of lines of code, literally.


You will see examples of this in the next chapters, about relational and NoSQL
databases, security, etc.


## FastAPI compatibility¶


The simplicity of the dependency injection system makes FastAPI compatible with:


• all the relational databases
• NoSQL databases
• external packages
• external APIs
• authentication and authorization systems
• API usage monitoring systems
• response data injection systems
• etc.


...
...
# Dependencies¶
...
## Simple and Powerful¶


Although the hierarchical dependency injection system is very simple to define
and use, it's still very powerful.


You can define dependencies that in turn can define dependencies themselves.


In the end, a hierarchical tree of dependencies is built, and the Dependency Injection system takes care of solving all these dependencies for you (and their
sub-dependencies) and providing (injecting) the results at each step.


For example, let's say you have 4 API endpoints (path operations):


• `/items/public/`
• `/items/private/`
• `/users/{user_id}/activate`
• `/items/pro/`


then you could add different permission requirements for each of them just with
dependencies and sub-dependencies:

         current_user active_user admin_user paying_user /items/public/ /items/private/ /users/{user_id}/activate /items/pro/
         ...
...
# Dependencies¶
...
## Integrated with OpenAPI¶


All these dependencies, while declaring their requirements, also add parameters,
validations, etc. to your path operations.


FastAPI will take care of adding it all to the OpenAPI schema, so that it is shown in
the interactive documentation systems.

[Image: Image]

 Back to top The FastAPI trademark is owned by [@tiangolo](https://tiangolo.com/) and is registered in the US and across other regionsMade with [Material for MkDocs](https://squidfunk.github.io/mkdocs-material/)[github.com](https://github.com/fastapi/fastapi)[discord.gg](https://discord.gg/VQjSZaeJmf)[twitter.com](https://twitter.com/fastapi)[www.linkedin.com](https://www.linkedin.com/company/fastapi)[tiangolo.com](https://tiangolo.com/) 

...
...
# Dependencies¶
...
## Additional Links
- [en - English](https://fastapi.tiangolo.com/)
- [az - azərbaycan dili](https://fastapi.tiangolo.com/az/)
- [bn - বাংলা](https://fastapi.tiangolo.com/bn/)
- [de - Deutsch](https://fastapi.tiangolo.com/de/)
- [es - español](https://fastapi.tiangolo.com/es/)
- [fa - فارسی](https://fastapi.tiangolo.com/fa/)
- [fr - français](https://fastapi.tiangolo.com/fr/)
- [he - עברית](https://fastapi.tiangolo.com/he/)
- [hu - magyar](https://fastapi.tiangolo.com/hu/)
- [id - Bahasa Indonesia](https://fastapi.tiangolo.com/id/)
- [it - italiano](https://fastapi.tiangolo.com/it/)
- [ja - 日本語](https://fastapi.tiangolo.com/ja/)
...
...
# Dependencies¶
...
## Additional Links
...
- [ko - 한국어](https://fastapi.tiangolo.com/ko/)
- [nl - Nederlands](https://fastapi.tiangolo.com/nl/)
- [pl - Polski](https://fastapi.tiangolo.com/pl/)
- [pt - português](https://fastapi.tiangolo.com/pt/)
- [ru - русский язык](https://fastapi.tiangolo.com/ru/)
- [tr - Türkçe](https://fastapi.tiangolo.com/tr/)
- [uk - українська мова](https://fastapi.tiangolo.com/uk/)
- [ur - اردو](https://fastapi.tiangolo.com/ur/)
- [vi - Tiếng Việt](https://fastapi.tiangolo.com/vi/)
- [yo - Yorùbá](https://fastapi.tiangolo.com/yo/)
...
...
# Dependencies¶
...
## Additional Links
...
- [zh - 简体中文](https://fastapi.tiangolo.com/zh/)
- [zh-hant - 繁體中文](https://fastapi.tiangolo.com/zh-hant/)
- [😉](https://fastapi.tiangolo.com/em/)
- [Share](https://fastapi.tiangolo.com/tutorial/dependencies/?q=) - Share
- [FastAPI](https://fastapi.tiangolo.com/) - FastAPI
- [fastapi/fastapi 0.116.0 87k 7.6k](https://github.com/fastapi/fastapi) - Go to repository
- [FastAPI](https://fastapi.tiangolo.com/)
- [Features](https://fastapi.tiangolo.com/features/)
- [Learn](https://fastapi.tiangolo.com/learn/)
- [Python Types Intro](https://fastapi.tiangolo.com/python-types/)
- [Concurrency and async / await](https://fastapi.tiangolo.com/async/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Environment Variables](https://fastapi.tiangolo.com/environment-variables/)
- [Virtual Environments](https://fastapi.tiangolo.com/virtual-environments/)
- [Tutorial - User Guide](https://fastapi.tiangolo.com/tutorial/)
- [First Steps](https://fastapi.tiangolo.com/tutorial/first-steps/)
- [Path Parameters](https://fastapi.tiangolo.com/tutorial/path-params/)
- [Query Parameters](https://fastapi.tiangolo.com/tutorial/query-params/)
- [Request Body](https://fastapi.tiangolo.com/tutorial/body/)
- [Query Parameters and String Validations](https://fastapi.tiangolo.com/tutorial/query-params-str-validations/)
- [Path Parameters and Numeric Validations](https://fastapi.tiangolo.com/tutorial/path-params-numeric-validations/)
- [Query Parameter Models](https://fastapi.tiangolo.com/tutorial/query-param-models/)
- [Body - Multiple Parameters](https://fastapi.tiangolo.com/tutorial/body-multiple-params/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Body - Fields](https://fastapi.tiangolo.com/tutorial/body-fields/)
- [Body - Nested Models](https://fastapi.tiangolo.com/tutorial/body-nested-models/)
- [Declare Request Example Data](https://fastapi.tiangolo.com/tutorial/schema-extra-example/)
- [Extra Data Types](https://fastapi.tiangolo.com/tutorial/extra-data-types/)
- [Cookie Parameters](https://fastapi.tiangolo.com/tutorial/cookie-params/)
- [Header Parameters](https://fastapi.tiangolo.com/tutorial/header-params/)
- [Cookie Parameter Models](https://fastapi.tiangolo.com/tutorial/cookie-param-models/)
- [Header Parameter Models](https://fastapi.tiangolo.com/tutorial/header-param-models/)
- [Response Model - Return Type](https://fastapi.tiangolo.com/tutorial/response-model/)
- [Extra Models](https://fastapi.tiangolo.com/tutorial/extra-models/)
- [Response Status Code](https://fastapi.tiangolo.com/tutorial/response-status-code/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Form Data](https://fastapi.tiangolo.com/tutorial/request-forms/)
- [Form Models](https://fastapi.tiangolo.com/tutorial/request-form-models/)
- [Request Files](https://fastapi.tiangolo.com/tutorial/request-files/)
- [Request Forms and Files](https://fastapi.tiangolo.com/tutorial/request-forms-and-files/)
- [Handling Errors](https://fastapi.tiangolo.com/tutorial/handling-errors/)
- [Path Operation Configuration](https://fastapi.tiangolo.com/tutorial/path-operation-configuration/)
- [JSON Compatible Encoder](https://fastapi.tiangolo.com/tutorial/encoder/)
- [Body - Updates](https://fastapi.tiangolo.com/tutorial/body-updates/)
- [Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/)
- [Classes as Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/classes-as-dependencies/)
- [Sub-dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/sub-dependencies/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Dependencies in path operation decorators](https://fastapi.tiangolo.com/tutorial/dependencies/dependencies-in-path-operation-decorators/)
- [Global Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/global-dependencies/)
- [Dependencies with yield](https://fastapi.tiangolo.com/tutorial/dependencies/dependencies-with-yield/)
- [Security](https://fastapi.tiangolo.com/tutorial/security/)
- [Security - First Steps](https://fastapi.tiangolo.com/tutorial/security/first-steps/)
- [Get Current User](https://fastapi.tiangolo.com/tutorial/security/get-current-user/)
- [Simple OAuth2 with Password and Bearer](https://fastapi.tiangolo.com/tutorial/security/simple-oauth2/)
- [OAuth2 with Password (and hashing), Bearer with JWT tokens](https://fastapi.tiangolo.com/tutorial/security/oauth2-jwt/)
- [Middleware](https://fastapi.tiangolo.com/tutorial/middleware/)
- [CORS (Cross-Origin Resource Sharing)](https://fastapi.tiangolo.com/tutorial/cors/)
...
...
# Dependencies¶
...
## Additional Links
...
- [SQL (Relational) Databases](https://fastapi.tiangolo.com/tutorial/sql-databases/)
- [Bigger Applications - Multiple Files](https://fastapi.tiangolo.com/tutorial/bigger-applications/)
- [Background Tasks](https://fastapi.tiangolo.com/tutorial/background-tasks/)
- [Metadata and Docs URLs](https://fastapi.tiangolo.com/tutorial/metadata/)
- [Static Files](https://fastapi.tiangolo.com/tutorial/static-files/)
- [Testing](https://fastapi.tiangolo.com/tutorial/testing/)
- [Debugging](https://fastapi.tiangolo.com/tutorial/debugging/)
- [Advanced User Guide](https://fastapi.tiangolo.com/advanced/)
- [Path Operation Advanced Configuration](https://fastapi.tiangolo.com/advanced/path-operation-advanced-configuration/)
- [Additional Status Codes](https://fastapi.tiangolo.com/advanced/additional-status-codes/)
- [Return a Response Directly](https://fastapi.tiangolo.com/advanced/response-directly/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Custom Response - HTML, Stream, File, others](https://fastapi.tiangolo.com/advanced/custom-response/)
- [Additional Responses in OpenAPI](https://fastapi.tiangolo.com/advanced/additional-responses/)
- [Response Cookies](https://fastapi.tiangolo.com/advanced/response-cookies/)
- [Response Headers](https://fastapi.tiangolo.com/advanced/response-headers/)
- [Response - Change Status Code](https://fastapi.tiangolo.com/advanced/response-change-status-code/)
- [Advanced Dependencies](https://fastapi.tiangolo.com/advanced/advanced-dependencies/)
- [Advanced Security](https://fastapi.tiangolo.com/advanced/security/)
- [OAuth2 scopes](https://fastapi.tiangolo.com/advanced/security/oauth2-scopes/)
- [HTTP Basic Auth](https://fastapi.tiangolo.com/advanced/security/http-basic-auth/)
- [Using the Request Directly](https://fastapi.tiangolo.com/advanced/using-request-directly/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Using Dataclasses](https://fastapi.tiangolo.com/advanced/dataclasses/)
- [Advanced Middleware](https://fastapi.tiangolo.com/advanced/middleware/)
- [Sub Applications - Mounts](https://fastapi.tiangolo.com/advanced/sub-applications/)
- [Behind a Proxy](https://fastapi.tiangolo.com/advanced/behind-a-proxy/)
- [Templates](https://fastapi.tiangolo.com/advanced/templates/)
- [WebSockets](https://fastapi.tiangolo.com/advanced/websockets/)
- [Lifespan Events](https://fastapi.tiangolo.com/advanced/events/)
- [Testing WebSockets](https://fastapi.tiangolo.com/advanced/testing-websockets/)
- [Testing Events: startup - shutdown](https://fastapi.tiangolo.com/advanced/testing-events/)
- [Testing Dependencies with Overrides](https://fastapi.tiangolo.com/advanced/testing-dependencies/)
- [Async Tests](https://fastapi.tiangolo.com/advanced/async-tests/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Settings and Environment Variables](https://fastapi.tiangolo.com/advanced/settings/)
- [OpenAPI Callbacks](https://fastapi.tiangolo.com/advanced/openapi-callbacks/)
- [OpenAPI Webhooks](https://fastapi.tiangolo.com/advanced/openapi-webhooks/)
- [Including WSGI - Flask, Django, others](https://fastapi.tiangolo.com/advanced/wsgi/)
- [Generate Clients](https://fastapi.tiangolo.com/advanced/generate-clients/)
- [FastAPI CLI](https://fastapi.tiangolo.com/fastapi-cli/)
- [Deployment](https://fastapi.tiangolo.com/deployment/)
- [About FastAPI versions](https://fastapi.tiangolo.com/deployment/versions/)
- [About HTTPS](https://fastapi.tiangolo.com/deployment/https/)
- [Run a Server Manually](https://fastapi.tiangolo.com/deployment/manually/)
- [Deployments Concepts](https://fastapi.tiangolo.com/deployment/concepts/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Deploy FastAPI on Cloud Providers](https://fastapi.tiangolo.com/deployment/cloud/)
- [Server Workers - Uvicorn with Workers](https://fastapi.tiangolo.com/deployment/server-workers/)
- [FastAPI in Containers - Docker](https://fastapi.tiangolo.com/deployment/docker/)
- [How To - Recipes](https://fastapi.tiangolo.com/how-to/)
- [General - How To - Recipes](https://fastapi.tiangolo.com/how-to/general/)
- [GraphQL](https://fastapi.tiangolo.com/how-to/graphql/)
- [Custom Request and APIRoute class](https://fastapi.tiangolo.com/how-to/custom-request-and-route/)
- [Conditional OpenAPI](https://fastapi.tiangolo.com/how-to/conditional-openapi/)
- [Extending OpenAPI](https://fastapi.tiangolo.com/how-to/extending-openapi/)
- [Separate OpenAPI Schemas for Input and Output or Not](https://fastapi.tiangolo.com/how-to/separate-openapi-schemas/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Custom Docs UI Static Assets (Self-Hosting)](https://fastapi.tiangolo.com/how-to/custom-docs-ui-assets/)
- [Configure Swagger UI](https://fastapi.tiangolo.com/how-to/configure-swagger-ui/)
- [Testing a Database](https://fastapi.tiangolo.com/how-to/testing-database/)
- [Reference](https://fastapi.tiangolo.com/reference/)
- [FastAPI class](https://fastapi.tiangolo.com/reference/fastapi/)
- [Request Parameters](https://fastapi.tiangolo.com/reference/parameters/)
- [Status Codes](https://fastapi.tiangolo.com/reference/status/)
- [UploadFile class](https://fastapi.tiangolo.com/reference/uploadfile/)
- [Exceptions - HTTPException and WebSocketException](https://fastapi.tiangolo.com/reference/exceptions/)
- [Dependencies - Depends() and Security()](https://fastapi.tiangolo.com/reference/dependencies/)
- [APIRouter class](https://fastapi.tiangolo.com/reference/apirouter/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Background Tasks - BackgroundTasks](https://fastapi.tiangolo.com/reference/background/)
- [Request class](https://fastapi.tiangolo.com/reference/request/)
- [WebSockets](https://fastapi.tiangolo.com/reference/websockets/)
- [HTTPConnection class](https://fastapi.tiangolo.com/reference/httpconnection/)
- [Response class](https://fastapi.tiangolo.com/reference/response/)
- [Custom Response Classes - File, HTML, Redirect, Streaming, etc.](https://fastapi.tiangolo.com/reference/responses/)
- [Middleware](https://fastapi.tiangolo.com/reference/middleware/)
- [OpenAPI](https://fastapi.tiangolo.com/reference/openapi/)
- [OpenAPI docs](https://fastapi.tiangolo.com/reference/openapi/docs/)
- [OpenAPI models](https://fastapi.tiangolo.com/reference/openapi/models/)
- [Security Tools](https://fastapi.tiangolo.com/reference/security/)
- [Encoders - jsonable_encoder](https://fastapi.tiangolo.com/reference/encoders/)
...
...
# Dependencies¶
...
## Additional Links
...
- [Static Files - StaticFiles](https://fastapi.tiangolo.com/reference/staticfiles/)
- [Templating - Jinja2Templates](https://fastapi.tiangolo.com/reference/templating/)
- [Test Client - TestClient](https://fastapi.tiangolo.com/reference/testclient/)
- [FastAPI People](https://fastapi.tiangolo.com/fastapi-people/)
- [Resources](https://fastapi.tiangolo.com/resources/)
- [Help FastAPI - Get Help](https://fastapi.tiangolo.com/help-fastapi/)
- [Development - Contributing](https://fastapi.tiangolo.com/contributing/)
- [Full Stack FastAPI Template](https://fastapi.tiangolo.com/project-generation/)
- [External Links and Articles](https://fastapi.tiangolo.com/external-links/)
- [FastAPI and friends newsletter](https://fastapi.tiangolo.com/newsletter/)
- [Repository Management Tasks](https://fastapi.tiangolo.com/management-tasks/)
...
...
# Dependencies¶
...
## Additional Links
...
- [About](https://fastapi.tiangolo.com/about/)
- [Alternatives, Inspiration and Comparisons](https://fastapi.tiangolo.com/alternatives/)
- [History, Design and Future](https://fastapi.tiangolo.com/history-design-future/)
- [Benchmarks](https://fastapi.tiangolo.com/benchmarks/)
- [Repository Management](https://fastapi.tiangolo.com/management/)
- [Release Notes](https://fastapi.tiangolo.com/release-notes/)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Learn](https://fastapi.tiangolo.com/learn/)
- [Tutorial - User Guide](https://fastapi.tiangolo.com/tutorial/)
- [Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/)
- [Previous: Body - Updates](https://fastapi.tiangolo.com/tutorial/body-updates/)
- [Next: Classes as Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/classes-as-dependencies/)
...
# Dependencies¶


FastAPI has a very powerful but intuitive Dependency Injection system.


It is designed to be very simple to use, and to make it very easy for any
developer to integrate other components with FastAPI.


## What is "Dependency Injection"¶
...
## First Steps¶
...
## Share `Annotated` dependencies¶
...
## To `async` or not to `async`¶
...
## Integrated with OpenAPI¶
...
## Simple usage¶
...
## FastAPI plug-ins¶
...
## FastAPI compatibility¶
...
## Simple and Powerful¶
...
## Integrated with OpenAPI¶
...
## Additional Links
...
  Skip to content  [ Subscribe to the FastAPI and friends newsletter 🎉](https://fastapi.tiangolo.com/newsletter/) [sponsor](https://zuplo.link/fastapi-web)   
# Dependencies¶
...
