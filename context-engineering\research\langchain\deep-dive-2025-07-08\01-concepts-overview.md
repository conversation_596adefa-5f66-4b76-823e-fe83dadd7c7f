[Skip to main content](https://python.langchain.com/docs/concepts/#__docusaurus_skipToContent_fallback)

**Our [Building Ambient Agents with LangGraph](https://academy.langchain.com/courses/ambient-agents/?utm_medium=internal&utm_source=docs&utm_campaign=q2-2025_ambient-agents_co) course is now available on LangChain Academy!**

On this page

[![Open on GitHub](https://img.shields.io/badge/Open%20on%20GitHub-grey?logo=github&logoColor=white)](https://github.com/langchain-ai/langchain/blob/master/docs/docs/concepts/index.mdx)

This guide provides explanations of the key concepts behind the LangChain framework and AI applications more broadly.

We recommend that you go through at least one of the [Tutorials](https://python.langchain.com/docs/tutorials/) before diving into the conceptual guide. This will provide practical context that will make it easier to understand the concepts discussed here.

The conceptual guide does not cover step-by-step instructions or specific implementation examples — those are found in the [How-to guides](https://python.langchain.com/docs/how_to/) and [Tutorials](https://python.langchain.com/docs/tutorials/). For detailed reference material, please see the [API reference](https://python.langchain.com/api_reference/).

## High level [​](https://python.langchain.com/docs/concepts/\#high-level "Direct link to High level")

- **[Why LangChain?](https://python.langchain.com/docs/concepts/why_langchain/)**: Overview of the value that LangChain provides.
- **[Architecture](https://python.langchain.com/docs/concepts/architecture/)**: How packages are organized in the LangChain ecosystem.

## Concepts [​](https://python.langchain.com/docs/concepts/\#concepts "Direct link to Concepts")

- **[Chat models](https://python.langchain.com/docs/concepts/chat_models/)**: LLMs exposed via a chat API that process sequences of messages as input and output a message.
- **[Messages](https://python.langchain.com/docs/concepts/messages/)**: The unit of communication in chat models, used to represent model input and output.
- **[Chat history](https://python.langchain.com/docs/concepts/chat_history/)**: A conversation represented as a sequence of messages, alternating between user messages and model responses.
- **[Tools](https://python.langchain.com/docs/concepts/tools/)**: A function with an associated schema defining the function's name, description, and the arguments it accepts.
- **[Tool calling](https://python.langchain.com/docs/concepts/tool_calling/)**: A type of chat model API that accepts tool schemas, along with messages, as input and returns invocations of those tools as part of the output message.
- **[Structured output](https://python.langchain.com/docs/concepts/structured_outputs/)**: A technique to make a chat model respond in a structured format, such as JSON that matches a given schema.
- **[Memory](https://langchain-ai.github.io/langgraph/concepts/memory/)**: Information about a conversation that is persisted so that it can be used in future conversations.
- **[Multimodality](https://python.langchain.com/docs/concepts/multimodality/)**: The ability to work with data that comes in different forms, such as text, audio, images, and video.
- **[Runnable interface](https://python.langchain.com/docs/concepts/runnables/)**: The base abstraction that many LangChain components and the LangChain Expression Language are built on.
- **[Streaming](https://python.langchain.com/docs/concepts/streaming/)**: LangChain streaming APIs for surfacing results as they are generated.
- **[LangChain Expression Language (LCEL)](https://python.langchain.com/docs/concepts/lcel/)**: A syntax for orchestrating LangChain components. Most useful for simpler applications.
- **[Document loaders](https://python.langchain.com/docs/concepts/document_loaders/)**: Load a source as a list of documents.
- **[Retrieval](https://python.langchain.com/docs/concepts/retrieval/)**: Information retrieval systems can retrieve structured or unstructured data from a datasource in response to a query.
- **[Text splitters](https://python.langchain.com/docs/concepts/text_splitters/)**: Split long text into smaller chunks that can be individually indexed to enable granular retrieval.
- **[Embedding models](https://python.langchain.com/docs/concepts/embedding_models/)**: Models that represent data such as text or images in a vector space.
- **[Vector stores](https://python.langchain.com/docs/concepts/vectorstores/)**: Storage of and efficient search over vectors and associated metadata.
- **[Retriever](https://python.langchain.com/docs/concepts/retrievers/)**: A component that returns relevant documents from a knowledge base in response to a query.
- **[Retrieval Augmented Generation (RAG)](https://python.langchain.com/docs/concepts/rag/)**: A technique that enhances language models by combining them with external knowledge bases.
- **[Agents](https://python.langchain.com/docs/concepts/agents/)**: Use a [language model](https://python.langchain.com/docs/concepts/chat_models/) to choose a sequence of actions to take. Agents can interact with external resources via [tool](https://python.langchain.com/docs/concepts/tools/).
- **[Prompt templates](https://python.langchain.com/docs/concepts/prompt_templates/)**: Component for factoring out the static parts of a model "prompt" (usually a sequence of messages). Useful for serializing, versioning, and reusing these static parts.
- **[Output parsers](https://python.langchain.com/docs/concepts/output_parsers/)**: Responsible for taking the output of a model and transforming it into a more suitable format for downstream tasks. Output parsers were primarily useful prior to the general availability of [tool calling](https://python.langchain.com/docs/concepts/tool_calling/) and [structured outputs](https://python.langchain.com/docs/concepts/structured_outputs/).
- **[Few-shot prompting](https://python.langchain.com/docs/concepts/few_shot_prompting/)**: A technique for improving model performance by providing a few examples of the task to perform in the prompt.
- **[Example selectors](https://python.langchain.com/docs/concepts/example_selectors/)**: Used to select the most relevant examples from a dataset based on a given input. Example selectors are used in few-shot prompting to select examples for a prompt.
- **[Async programming](https://python.langchain.com/docs/concepts/async/)**: The basics that one should know to use LangChain in an asynchronous context.
- **[Callbacks](https://python.langchain.com/docs/concepts/callbacks/)**: Callbacks enable the execution of custom auxiliary code in built-in components. Callbacks are used to stream outputs from LLMs in LangChain, trace the intermediate steps of an application, and more.
- **[Tracing](https://python.langchain.com/docs/concepts/tracing/)**: The process of recording the steps that an application takes to go from input to output. Tracing is essential for debugging and diagnosing issues in complex applications.
- **[Evaluation](https://python.langchain.com/docs/concepts/evaluation/)**: The process of assessing the performance and effectiveness of AI applications. This involves testing the model's responses against a set of predefined criteria or benchmarks to ensure it meets the desired quality standards and fulfills the intended purpose. This process is vital for building reliable applications.
- **[Testing](https://python.langchain.com/docs/concepts/testing/)**: The process of verifying that a component of an integration or application works as expected. Testing is essential for ensuring that the application behaves correctly and that changes to the codebase do not introduce new bugs.

## Glossary [​](https://python.langchain.com/docs/concepts/\#glossary "Direct link to Glossary")

- **[AIMessageChunk](https://python.langchain.com/docs/concepts/messages/#aimessagechunk)**: A partial response from an AI message. Used when streaming responses from a chat model.
- **[AIMessage](https://python.langchain.com/docs/concepts/messages/#aimessage)**: Represents a complete response from an AI model.
- **[astream\_events](https://python.langchain.com/docs/concepts/chat_models/#key-methods)**: Stream granular information from [LCEL](https://python.langchain.com/docs/concepts/lcel/) chains.
- **[BaseTool](https://python.langchain.com/docs/concepts/tools/#tool-interface)**: The base class for all tools in LangChain.
- **[batch](https://python.langchain.com/docs/concepts/runnables/)**: Use to execute a runnable with batch inputs.
- **[bind\_tools](https://python.langchain.com/docs/concepts/tool_calling/#tool-binding)**: Allows models to interact with tools.
- **[Caching](https://python.langchain.com/docs/concepts/chat_models/#caching)**: Storing results to avoid redundant calls to a chat model.
- **[Chat models](https://python.langchain.com/docs/concepts/multimodality/#multimodality-in-chat-models)**: Chat models that handle multiple data modalities.
- **[Configurable runnables](https://python.langchain.com/docs/concepts/runnables/#configurable-runnables)**: Creating configurable Runnables.
- **[Context window](https://python.langchain.com/docs/concepts/chat_models/#context-window)**: The maximum size of input a chat model can process.
- **[Conversation patterns](https://python.langchain.com/docs/concepts/chat_history/#conversation-patterns)**: Common patterns in chat interactions.
- **[Document](https://python.langchain.com/api_reference/core/documents/langchain_core.documents.base.Document.html)**: LangChain's representation of a document.
- **[Embedding models](https://python.langchain.com/docs/concepts/multimodality/#multimodality-in-embedding-models)**: Models that generate vector embeddings for various data types.
- **[HumanMessage](https://python.langchain.com/docs/concepts/messages/#humanmessage)**: Represents a message from a human user.
- **[InjectedState](https://python.langchain.com/docs/concepts/tools/#injectedstate)**: A state injected into a tool function.
- **[InjectedStore](https://python.langchain.com/docs/concepts/tools/#injectedstore)**: A store that can be injected into a tool for data persistence.
- **[InjectedToolArg](https://python.langchain.com/docs/concepts/tools/#injectedtoolarg)**: Mechanism to inject arguments into tool functions.
- **[input and output types](https://python.langchain.com/docs/concepts/runnables/#input-and-output-types)**: Types used for input and output in Runnables.
- **[Integration packages](https://python.langchain.com/docs/concepts/architecture/#integration-packages)**: Third-party packages that integrate with LangChain.
- **[Integration tests](https://python.langchain.com/docs/concepts/testing/#integration-tests)**: Tests that verify the correctness of the interaction between components, usually run with access to the underlying API that powers an integration.
- **[invoke](https://python.langchain.com/docs/concepts/runnables/)**: A standard method to invoke a Runnable.
- **[JSON mode](https://python.langchain.com/docs/concepts/structured_outputs/#json-mode)**: Returning responses in JSON format.
- **[langchain-community](https://python.langchain.com/docs/concepts/architecture/#langchain-community)**: Community-driven components for LangChain.
- **[langchain-core](https://python.langchain.com/docs/concepts/architecture/#langchain-core)**: Core langchain package. Includes base interfaces and in-memory implementations.
- **[langchain](https://python.langchain.com/docs/concepts/architecture/#langchain)**: A package for higher level components (e.g., some pre-built chains).
- **[langgraph](https://python.langchain.com/docs/concepts/architecture/#langgraph)**: Powerful orchestration layer for LangChain. Use to build complex pipelines and workflows.
- **[langserve](https://python.langchain.com/docs/concepts/architecture/#langserve)**: Used to deploy LangChain Runnables as REST endpoints. Uses FastAPI. Works primarily for LangChain Runnables, does not currently integrate with LangGraph.
- **[LLMs (legacy)](https://python.langchain.com/docs/concepts/text_llms/)**: Older language models that take a string as input and return a string as output.
- **[Managing chat history](https://python.langchain.com/docs/concepts/chat_history/#managing-chat-history)**: Techniques to maintain and manage the chat history.
- **[OpenAI format](https://python.langchain.com/docs/concepts/messages/#openai-format)**: OpenAI's message format for chat models.
- **[Propagation of RunnableConfig](https://python.langchain.com/docs/concepts/runnables/#propagation-of-runnableconfig)**: Propagating configuration through Runnables. Read if working with python 3.9, 3.10 and async.
- **[rate-limiting](https://python.langchain.com/docs/concepts/chat_models/#rate-limiting)**: Client side rate limiting for chat models.
- **[RemoveMessage](https://python.langchain.com/docs/concepts/messages/#removemessage)**: An abstraction used to remove a message from chat history, used primarily in LangGraph.
- **[role](https://python.langchain.com/docs/concepts/messages/#role)**: Represents the role (e.g., user, assistant) of a chat message.
- **[RunnableConfig](https://python.langchain.com/docs/concepts/runnables/#runnableconfig)**: Use to pass run time information to Runnables (e.g., `run_name`, `run_id`, `tags`, `metadata`, `max_concurrency`, `recursion_limit`, `configurable`).
- **[Standard parameters for chat models](https://python.langchain.com/docs/concepts/chat_models/#standard-parameters)**: Parameters such as API key, `temperature`, and `max_tokens`.
- **[Standard tests](https://python.langchain.com/docs/concepts/testing/#standard-tests)**: A defined set of unit and integration tests that all integrations must pass.
- **[stream](https://python.langchain.com/docs/concepts/streaming/)**: Use to stream output from a Runnable or a graph.
- **[Tokenization](https://python.langchain.com/docs/concepts/tokens/)**: The process of converting data into tokens and vice versa.
- **[Tokens](https://python.langchain.com/docs/concepts/tokens/)**: The basic unit that a language model reads, processes, and generates under the hood.
- **[Tool artifacts](https://python.langchain.com/docs/concepts/tools/#tool-artifacts)**: Add artifacts to the output of a tool that will not be sent to the model, but will be available for downstream processing.
- **[Tool binding](https://python.langchain.com/docs/concepts/tool_calling/#tool-binding)**: Binding tools to models.
- **[@tool](https://python.langchain.com/docs/concepts/tools/#create-tools-using-the-tool-decorator)**: Decorator for creating tools in LangChain.
- **[Toolkits](https://python.langchain.com/docs/concepts/tools/#toolkits)**: A collection of tools that can be used together.
- **[ToolMessage](https://python.langchain.com/docs/concepts/messages/#toolmessage)**: Represents a message that contains the results of a tool execution.
- **[Unit tests](https://python.langchain.com/docs/concepts/testing/#unit-tests)**: Tests that verify the correctness of individual components, run in isolation without access to the Internet.
- **[Vector stores](https://python.langchain.com/docs/concepts/vectorstores/)**: Datastores specialized for storing and efficiently searching vector embeddings.
- **[with\_structured\_output](https://python.langchain.com/docs/concepts/structured_outputs/#structured-output-method)**: A helper method for chat models that natively support [tool calling](https://python.langchain.com/docs/concepts/tool_calling/) to get structured output matching a given schema specified via Pydantic, JSON schema or a function.
- **[with\_types](https://python.langchain.com/docs/concepts/runnables/#with_types)**: Method to overwrite the input and output types of a runnable. Useful when working with complex LCEL chains and deploying with LangServe.

- [High level](https://python.langchain.com/docs/concepts/#high-level)
- [Concepts](https://python.langchain.com/docs/concepts/#concepts)
- [Glossary](https://python.langchain.com/docs/concepts/#glossary)
