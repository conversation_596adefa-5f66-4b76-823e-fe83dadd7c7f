[Skip to main content](https://python.langchain.com/docs/how_to/tool_calling/#__docusaurus_skipToContent_fallback)

**Our [Building Ambient Agents with LangGraph](https://academy.langchain.com/courses/ambient-agents/?utm_medium=internal&utm_source=docs&utm_campaign=q2-2025_ambient-agents_co) course is now available on LangChain Academy!**

On this page

[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain/blob/master/docs/docs/how_to/tool_calling.ipynb)[![Open on GitHub](https://img.shields.io/badge/Open%20on%20GitHub-grey?logo=github&logoColor=white)](https://github.com/langchain-ai/langchain/blob/master/docs/docs/how_to/tool_calling.ipynb)

Prerequisites

This guide assumes familiarity with the following concepts:

- [Chat models](https://python.langchain.com/docs/concepts/chat_models/)
- [Tool calling](https://python.langchain.com/docs/concepts/tool_calling/)
- [Tools](https://python.langchain.com/docs/concepts/tools/)
- [Output parsers](https://python.langchain.com/docs/concepts/output_parsers/)

[Tool calling](https://python.langchain.com/docs/concepts/tool_calling/) allows a chat model to respond to a given prompt by "calling a tool".

Remember, while the name "tool calling" implies that the model is directly performing some action, this is actually not the case! The model only generates the arguments to a tool, and actually running the tool (or not) is up to the user.

Tool calling is a general technique that generates structured output from a model, and you can use it even when you don't intend to invoke any tools. An example use-case of that is [extraction from unstructured text](https://python.langchain.com/docs/tutorials/extraction/).

![Diagram of calling a tool](https://python.langchain.com/assets/images/tool_call-8d4a8b18e90cacd03f62e94071eceace.png)

If you want to see how to use the model-generated tool call to actually run a tool [check out this guide](https://python.langchain.com/docs/how_to/tool_results_pass_to_model/).

Supported models

Tool calling is not universal, but is supported by many popular LLM providers. You can find a [list of all models that support tool calling here](https://python.langchain.com/docs/integrations/chat/).

LangChain implements standard interfaces for defining tools, passing them to LLMs, and representing tool calls.
This guide will cover how to bind tools to an LLM, then invoke the LLM to generate these arguments.

## Defining tool schemas [​](https://python.langchain.com/docs/how_to/tool_calling/\#defining-tool-schemas "Direct link to Defining tool schemas")

For a model to be able to call tools, we need to pass in tool schemas that describe what the tool does and what it's arguments are. Chat models that support tool calling features implement a `.bind_tools()` method for passing tool schemas to the model. Tool schemas can be passed in as Python functions (with typehints and docstrings), Pydantic models, TypedDict classes, or LangChain [Tool objects](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.BaseTool.html#basetool). Subsequent invocations of the model will pass in these tool schemas along with the prompt.

### Python functions [​](https://python.langchain.com/docs/how_to/tool_calling/\#python-functions "Direct link to Python functions")

Our tool schemas can be Python functions:

```codeBlockLines_e6Vv
# The function name, type hints, and docstring are all part of the tool
# schema that's passed to the model. Defining good, descriptive schemas
# is an extension of prompt engineering and is an important part of
# getting models to perform well.
def add(a: int, b: int) -> int:
    """Add two integers.

    Args:
        a: First integer
        b: Second integer
    """
    return a + b

def multiply(a: int, b: int) -> int:
    """Multiply two integers.

    Args:
        a: First integer
        b: Second integer
    """
    return a * b

```

### LangChain Tool [​](https://python.langchain.com/docs/how_to/tool_calling/\#langchain-tool "Direct link to LangChain Tool")

LangChain also implements a `@tool` decorator that allows for further control of the tool schema, such as tool names and argument descriptions. See the how-to guide [here](https://python.langchain.com/docs/how_to/custom_tools/#creating-tools-from-functions) for details.

### Pydantic class [​](https://python.langchain.com/docs/how_to/tool_calling/\#pydantic-class "Direct link to Pydantic class")

You can equivalently define the schemas without the accompanying functions using [Pydantic](https://docs.pydantic.dev/).

Note that all fields are `required` unless provided a default value.

```codeBlockLines_e6Vv
from pydantic import BaseModel, Field

class add(BaseModel):
    """Add two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")

class multiply(BaseModel):
    """Multiply two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")

```

### TypedDict class [​](https://python.langchain.com/docs/how_to/tool_calling/\#typeddict-class "Direct link to TypedDict class")

Requires `langchain-core>=0.2.25`

Or using TypedDicts and annotations:

```codeBlockLines_e6Vv
from typing_extensions import Annotated, TypedDict

class add(TypedDict):
    """Add two integers."""

    # Annotations must have the type and can optionally include a default value and description (in that order).
    a: Annotated[int, ..., "First integer"]
    b: Annotated[int, ..., "Second integer"]

class multiply(TypedDict):
    """Multiply two integers."""

    a: Annotated[int, ..., "First integer"]
    b: Annotated[int, ..., "Second integer"]

tools = [add, multiply]

```

To actually bind those schemas to a chat model, we'll use the `.bind_tools()` method. This handles converting
the `add` and `multiply` schemas to the proper format for the model. The tool schema will then be passed it in each time the model is invoked.

Select [chat model](https://python.langchain.com/docs/integrations/chat/):

Google Gemini▾

[OpenAI](https://python.langchain.com/docs/how_to/tool_calling/#)
[Anthropic](https://python.langchain.com/docs/how_to/tool_calling/#)
[Azure](https://python.langchain.com/docs/how_to/tool_calling/#)
[Google Gemini](https://python.langchain.com/docs/how_to/tool_calling/#)
[Google Vertex](https://python.langchain.com/docs/how_to/tool_calling/#)
[AWS](https://python.langchain.com/docs/how_to/tool_calling/#)
[Groq](https://python.langchain.com/docs/how_to/tool_calling/#)
[Cohere](https://python.langchain.com/docs/how_to/tool_calling/#)
[NVIDIA](https://python.langchain.com/docs/how_to/tool_calling/#)
[Fireworks AI](https://python.langchain.com/docs/how_to/tool_calling/#)
[Mistral AI](https://python.langchain.com/docs/how_to/tool_calling/#)
[Together AI](https://python.langchain.com/docs/how_to/tool_calling/#)
[IBM watsonx](https://python.langchain.com/docs/how_to/tool_calling/#)
[Databricks](https://python.langchain.com/docs/how_to/tool_calling/#)
[xAI](https://python.langchain.com/docs/how_to/tool_calling/#)
[Perplexity](https://python.langchain.com/docs/how_to/tool_calling/#)

```codeBlockLines_e6Vv
pip install -qU "langchain[google-genai]"

```

```codeBlockLines_e6Vv
import getpass
import os

if not os.environ.get("GOOGLE_API_KEY"):
  os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")

from langchain.chat_models import init_chat_model

llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

```

```codeBlockLines_e6Vv
llm_with_tools = llm.bind_tools(tools)

query = "What is 3 * 12?"

llm_with_tools.invoke(query)

```

```codeBlockLines_e6Vv
AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_iXj4DiW1p7WLjTAQMRO0jxMs', 'function': {'arguments': '{"a":3,"b":12}', 'name': 'multiply'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 17, 'prompt_tokens': 80, 'total_tokens': 97}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_483d39d857', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-0b620986-3f62-4df7-9ba3-4595089f9ad4-0', tool_calls=[{'name': 'multiply', 'args': {'a': 3, 'b': 12}, 'id': 'call_iXj4DiW1p7WLjTAQMRO0jxMs', 'type': 'tool_call'}], usage_metadata={'input_tokens': 80, 'output_tokens': 17, 'total_tokens': 97})

```

As we can see our LLM generated arguments to a tool! You can look at the docs for [bind\_tools()](https://python.langchain.com/api_reference/openai/chat_models/langchain_openai.chat_models.base.BaseChatOpenAI.html#langchain_openai.chat_models.base.BaseChatOpenAI.bind_tools) to learn about all the ways to customize how your LLM selects tools, as well as [this guide on how to force the LLM to call a tool](https://python.langchain.com/docs/how_to/tool_choice/) rather than letting it decide.

## Tool calls [​](https://python.langchain.com/docs/how_to/tool_calling/\#tool-calls "Direct link to Tool calls")

If tool calls are included in a LLM response, they are attached to the corresponding
[message](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessage.html#langchain_core.messages.ai.AIMessage)
or [message chunk](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessageChunk.html#langchain_core.messages.ai.AIMessageChunk)
as a list of [tool call](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolCall.html#langchain_core.messages.tool.ToolCall)
objects in the `.tool_calls` attribute.

Note that chat models can call multiple tools at once.

A `ToolCall` is a typed dict that includes a
tool name, dict of argument values, and (optionally) an identifier. Messages with no
tool calls default to an empty list for this attribute.

```codeBlockLines_e6Vv
query = "What is 3 * 12? Also, what is 11 + 49?"

llm_with_tools.invoke(query).tool_calls

```

```codeBlockLines_e6Vv
[{'name': 'multiply',\
  'args': {'a': 3, 'b': 12},\
  'id': 'call_1fyhJAbJHuKQe6n0PacubGsL',\
  'type': 'tool_call'},\
 {'name': 'add',\
  'args': {'a': 11, 'b': 49},\
  'id': 'call_fc2jVkKzwuPWyU7kS9qn1hyG',\
  'type': 'tool_call'}]

```

The `.tool_calls` attribute should contain valid tool calls. Note that on occasion,
model providers may output malformed tool calls (e.g., arguments that are not
valid JSON). When parsing fails in these cases, instances
of [InvalidToolCall](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.InvalidToolCall.html#langchain_core.messages.tool.InvalidToolCall)
are populated in the `.invalid_tool_calls` attribute. An `InvalidToolCall` can have
a name, string arguments, identifier, and error message.

## Parsing [​](https://python.langchain.com/docs/how_to/tool_calling/\#parsing "Direct link to Parsing")

If desired, [output parsers](https://python.langchain.com/docs/how_to/#output-parsers) can further process the output. For example, we can convert existing values populated on the `.tool_calls` to Pydantic objects using the
[PydanticToolsParser](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_tools.PydanticToolsParser.html):

```codeBlockLines_e6Vv
from langchain_core.output_parsers import PydanticToolsParser
from pydantic import BaseModel, Field

class add(BaseModel):
    """Add two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")

class multiply(BaseModel):
    """Multiply two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")

chain = llm_with_tools | PydanticToolsParser(tools=[add, multiply])
chain.invoke(query)

```

**API Reference:** [PydanticToolsParser](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_tools.PydanticToolsParser.html)

```codeBlockLines_e6Vv
[multiply(a=3, b=12), add(a=11, b=49)]

```

## Next steps [​](https://python.langchain.com/docs/how_to/tool_calling/\#next-steps "Direct link to Next steps")

Now you've learned how to bind tool schemas to a chat model and have the model call the tool.

Next, check out this guide on actually using the tool by invoking the function and passing the results back to the model:

- Pass [tool results back to model](https://python.langchain.com/docs/how_to/tool_results_pass_to_model/)

You can also check out some more specific uses of tool calling:

- Getting [structured outputs](https://python.langchain.com/docs/how_to/structured_output/) from models
- Few shot prompting [with tools](https://python.langchain.com/docs/how_to/tools_few_shot/)
- Stream [tool calls](https://python.langchain.com/docs/how_to/tool_streaming/)
- Pass [runtime values to tools](https://python.langchain.com/docs/how_to/tool_runtime/)

- [Defining tool schemas](https://python.langchain.com/docs/how_to/tool_calling/#defining-tool-schemas)
  - [Python functions](https://python.langchain.com/docs/how_to/tool_calling/#python-functions)
  - [LangChain Tool](https://python.langchain.com/docs/how_to/tool_calling/#langchain-tool)
  - [Pydantic class](https://python.langchain.com/docs/how_to/tool_calling/#pydantic-class)
  - [TypedDict class](https://python.langchain.com/docs/how_to/tool_calling/#typeddict-class)
- [Tool calls](https://python.langchain.com/docs/how_to/tool_calling/#tool-calls)
- [Parsing](https://python.langchain.com/docs/how_to/tool_calling/#parsing)
- [Next steps](https://python.langchain.com/docs/how_to/tool_calling/#next-steps)
