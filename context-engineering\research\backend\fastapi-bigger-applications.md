# FastAPI - Bigger Applications - Multiple Files

This document summarizes the official FastAPI documentation on structuring bigger applications with multiple files.
The original documentation can be found at https://fastapi.tiangolo.com/tutorial/bigger-applications/.

## Introduction

For larger applications, it's not practical to keep all the code in a single file. FastAPI provides `APIRouter` to help structure your application across multiple files and directories.

## Example File Structure

A common way to structure a FastAPI project is to use a Python package.

```
.
├── app
│   ├── __init__.py
│   ├── main.py
│   ├── dependencies.py
│   └── routers
│       ├── __init__.py
│       ├── items.py
│       └── users.py
```

- `app/`: The main Python package for your application.
- `app/__init__.py`: Makes the `app` directory a Python package.
- `app/main.py`: The main file where the `FastAPI` app is created and routers are included.
- `app/dependencies.py`: A module for shared dependencies.
- `app/routers/`: A subpackage for different API routers.
- `app/routers/items.py`: A module containing path operations related to items.
- `app/routers/users.py`: A module containing path operations related to users.

## `APIRouter`

`APIRouter` works like a "mini `FastAPI`" class. You can declare path operations on it, and then include it in the main `FastAPI` application.

### Creating a Router

In `app/routers/users.py`:

```python
from fastapi import APIRouter

router = APIRouter()


@router.get("/users/", tags=["users"])
async def read_users():
    return [{"username": "Rick"}, {"username": "Morty"}]
```

### Including a Router in the Main App

In `app/main.py`, you can import and include the router:

```python
from fastapi import FastAPI
from .routers import users, items

app = FastAPI()

app.include_router(users.router)
app.include_router(items.router)
```

### Router Configuration

When creating an `APIRouter` or including it, you can specify a `prefix`, `tags`, `dependencies`, and `responses`. This helps to avoid code duplication.

In `app/routers/items.py`:

```python
from fastapi import APIRouter, Depends
from ..dependencies import get_token_header

router = APIRouter(
    prefix="/items",
    tags=["items"],
    dependencies=[Depends(get_token_header)],
    responses={404: {"description": "Not found"}},
)

# ... path operations for items
```

The `prefix="/items"` will be prepended to all path operations in this router.

You can also add these configurations when including the router in the main app, which is useful for reusable routers.

In `app/main.py`:

```python
from fastapi import FastAPI, Depends
from .internal import admin
from .dependencies import get_token_header

app = FastAPI()

app.include_router(
    admin.router,
    prefix="/admin",
    tags=["admin"],
    dependencies=[Depends(get_token_header)],
    responses={418: {"description": "I'm a teapot"}},
)
```

This allows you to structure your application logically, keeping related endpoints together while maintaining a clean and organized codebase.
