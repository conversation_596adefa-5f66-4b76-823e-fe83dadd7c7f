
from fastapi import FastAPI
from db.database import engine
from db.models import Student

app = FastAPI()

@app.post("/students/")
async def create_student(student: Student):
    await engine.save(student)
    return student

@app.get("/students/")
async def get_students():
    students = await engine.find(Student)
    return students

@app.get("/students/{id}")
async def get_student(id: str):
    student = await engine.find_one(Student, Student.id == id)
    return student

@app.put("/students/{id}")
async def update_student(id: str, student: Student):
    db_student = await engine.find_one(Student, Student.id == id)
    if db_student:
        # Update the fields
        for key, value in student.dict().items():
            setattr(db_student, key, value)
        await engine.save(db_student)
        return db_student
    return {"error": "Student not found"}

@app.delete("/students/{id}")
async def delete_student(id: str):
    student = await engine.find_one(Student, Student.id == id)
    if student:
        await engine.delete(student)
        return {"message": "Student deleted successfully"}
    return {"error": "Student not found"}
