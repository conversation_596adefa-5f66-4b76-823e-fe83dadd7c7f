// Modern Vanilla JavaScript App Template - 2025
// This template demonstrates ES2024/ES2025 features and best practices

// =============================================================================
// 1. MODERN MODULE STRUCTURE
// =============================================================================

// utils/state-manager.js
export class StateManager {
  #state = {};
  #listeners = new Map();
  #middleware = [];

  constructor(initialState = {}) {
    this.#state = { ...initialState };
  }

  // Using private fields (ES2022+)
  subscribe(key, callback) {
    if (!this.#listeners.has(key)) {
      this.#listeners.set(key, new Set());
    }
    this.#listeners.get(key).add(callback);
    
    return () => this.#listeners.get(key)?.delete(callback);
  }

  setState(updates) {
    const prevState = { ...this.#state };
    this.#state = { ...this.#state, ...updates };
    
    // Notify only changed properties
    Object.keys(updates).forEach(key => {
      if (prevState[key] !== this.#state[key]) {
        this.#listeners.get(key)?.forEach(callback => {
          callback(this.#state[key], prevState[key]);
        });
      }
    });
  }

  getState() {
    return { ...this.#state };
  }
}

// =============================================================================
// 2. PROMISE.WITHRESOLVERS() - ES2025 FEATURE
// =============================================================================

export class EventBus {
  #events = new Map();

  // Using the new Promise.withResolvers() method
  waitForEvent(eventName, timeout = 5000) {
    const { promise, resolve, reject } = Promise.withResolvers();
    
    if (!this.#events.has(eventName)) {
      this.#events.set(eventName, new Set());
    }
    
    this.#events.get(eventName).add(resolve);
    
    // Add timeout
    const timeoutId = setTimeout(() => {
      this.#events.get(eventName)?.delete(resolve);
      reject(new Error(`Event '${eventName}' timed out after ${timeout}ms`));
    }, timeout);
    
    // Clean up timeout when promise resolves
    promise.finally(() => clearTimeout(timeoutId));
    
    return promise;
  }

  emit(eventName, data) {
    const listeners = this.#events.get(eventName);
    if (listeners) {
      listeners.forEach(resolve => resolve(data));
      listeners.clear();
    }
  }
}

// =============================================================================
// 3. MODERN API CLIENT WITH SECURITY
// =============================================================================

export class SecureApiClient {
  #baseUrl;
  #defaultHeaders;

  constructor(baseUrl, options = {}) {
    this.#baseUrl = baseUrl;
    this.#defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      ...options.headers
    };
  }

  async #request(endpoint, options = {}) {
    const url = new URL(endpoint, this.#baseUrl);
    
    // Security: Validate URL origin
    if (url.origin !== new URL(this.#baseUrl).origin) {
      throw new Error('Invalid URL origin');
    }

    const config = {
      ...options,
      headers: {
        ...this.#defaultHeaders,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url.toString(), config);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      // Security: Check content type
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        throw new Error('Invalid content type');
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', { endpoint, error: error.message });
      throw error;
    }
  }

  // Public methods using optional chaining and nullish coalescing
  async get(endpoint, params = {}) {
    const url = new URL(endpoint, this.#baseUrl);
    Object.entries(params).forEach(([key, value]) => {
      if (value != null) url.searchParams.set(key, value);
    });
    
    return this.#request(url.pathname + url.search);
  }

  async post(endpoint, data) {
    return this.#request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async put(endpoint, data) {
    return this.#request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async delete(endpoint) {
    return this.#request(endpoint, { method: 'DELETE' });
  }
}

// =============================================================================
// 4. COMPONENT BASE CLASS WITH MODERN PATTERNS
// =============================================================================

export class Component {
  #element;
  #state;
  #listeners = new Set();
  #observers = new Set();
  #timers = new Set();

  constructor(selector, initialState = {}) {
    this.#element = document.querySelector(selector);
    if (!this.#element) {
      throw new Error(`Element not found: ${selector}`);
    }
    
    this.#state = { ...this.defaultState, ...initialState };
    this.init();
  }

  get defaultState() {
    return {};
  }

  get element() {
    return this.#element;
  }

  get state() {
    return { ...this.#state };
  }

  // State management with automatic re-rendering
  setState(updates) {
    const hasChanges = Object.keys(updates).some(
      key => this.#state[key] !== updates[key]
    );
    
    if (hasChanges) {
      this.#state = { ...this.#state, ...updates };
      this.render();
    }
  }

  // Event listener management with automatic cleanup
  addEventListener(element, event, handler, options = {}) {
    element.addEventListener(event, handler, options);
    this.#listeners.add({ element, event, handler, options });
  }

  // Observer management
  addObserver(observer) {
    this.#observers.add(observer);
  }

  // Timer management
  setTimeout(callback, delay) {
    const id = setTimeout(() => {
      callback();
      this.#timers.delete(id);
    }, delay);
    this.#timers.add(id);
    return id;
  }

  setInterval(callback, interval) {
    const id = setInterval(callback, interval);
    this.#timers.add(id);
    return id;
  }

  // Safe HTML rendering with XSS protection
  sanitizeHTML(str) {
    const div = document.createElement('div');
    div.textContent = str ?? '';
    return div.innerHTML;
  }

  // Template rendering with modern string interpolation
  template(strings, ...values) {
    return strings.reduce((result, string, i) => {
      const value = values[i];
      const sanitizedValue = typeof value === 'string' 
        ? this.sanitizeHTML(value) 
        : value ?? '';
      return result + string + sanitizedValue;
    }, '');
  }

  // Lifecycle methods
  init() {
    this.render();
    this.bindEvents();
  }

  render() {
    // Override in subclasses
  }

  bindEvents() {
    // Override in subclasses
  }

  // Cleanup with automatic resource management
  destroy() {
    // Remove event listeners
    this.#listeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });

    // Disconnect observers
    this.#observers.forEach(observer => {
      if (typeof observer.disconnect === 'function') {
        observer.disconnect();
      }
    });

    // Clear timers
    this.#timers.forEach(id => {
      clearTimeout(id);
      clearInterval(id);
    });

    // Clear collections
    this.#listeners.clear();
    this.#observers.clear();
    this.#timers.clear();
  }
}

// =============================================================================
// 5. PRACTICAL EXAMPLE: TODO LIST COMPONENT
// =============================================================================

export class TodoList extends Component {
  #api;

  constructor(selector, apiClient) {
    super(selector, {
      todos: [],
      filter: 'all',
      loading: false
    });
    this.#api = apiClient;
  }

  get defaultState() {
    return {
      todos: [],
      filter: 'all',
      loading: false
    };
  }

  async init() {
    super.init();
    await this.loadTodos();
    this.setupIntersectionObserver();
  }

  async loadTodos() {
    try {
      this.setState({ loading: true });
      const todos = await this.#api.get('/todos');
      this.setState({ todos, loading: false });
    } catch (error) {
      console.error('Failed to load todos:', error);
      this.setState({ loading: false });
    }
  }

  setupIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    });

    this.addObserver(observer);

    // Observe todo items when they're rendered
    this.element.querySelectorAll('.todo-item').forEach(item => {
      observer.observe(item);
    });
  }

  render() {
    const { todos, filter, loading } = this.state;
    const filteredTodos = this.getFilteredTodos(todos, filter);

    this.element.innerHTML = this.template`
      <div class="todo-container">
        <div class="todo-header">
          <h2>Todo List</h2>
          <div class="filter-buttons">
            <button class="filter-btn ${filter === 'all' ? 'active' : ''}" data-filter="all">
              All (${todos.length})
            </button>
            <button class="filter-btn ${filter === 'active' ? 'active' : ''}" data-filter="active">
              Active (${todos.filter(t => !t.completed).length})
            </button>
            <button class="filter-btn ${filter === 'completed' ? 'active' : ''}" data-filter="completed">
              Completed (${todos.filter(t => t.completed).length})
            </button>
          </div>
        </div>
        
        ${loading ? '<div class="loading">Loading...</div>' : ''}
        
        <div class="todo-list">
          ${filteredTodos.map(todo => this.renderTodo(todo)).join('')}
        </div>
        
        <div class="todo-input">
          <input type="text" placeholder="Add new todo..." class="new-todo-input">
          <button class="add-btn">Add</button>
        </div>
      </div>
    `;

    // Re-setup observer for new items
    this.setupIntersectionObserver();
  }

  renderTodo(todo) {
    return this.template`
      <div class="todo-item ${todo.completed ? 'completed' : ''}" data-id="${todo.id}">
        <input type="checkbox" class="todo-checkbox" ${todo.completed ? 'checked' : ''}>
        <span class="todo-text">${todo.text}</span>
        <div class="todo-actions">
          <button class="edit-btn" title="Edit">✏️</button>
          <button class="delete-btn" title="Delete">🗑️</button>
        </div>
      </div>
    `;
  }

  bindEvents() {
    // Use event delegation for better performance
    this.addEventListener(this.element, 'click', this.handleClick.bind(this));
    this.addEventListener(this.element, 'change', this.handleChange.bind(this));
    this.addEventListener(this.element, 'keypress', this.handleKeyPress.bind(this));
  }

  handleClick(event) {
    const { target } = event;
    const todoItem = target.closest('.todo-item');
    const todoId = todoItem?.dataset.id;

    if (target.classList.contains('delete-btn') && todoId) {
      this.deleteTodo(todoId);
    } else if (target.classList.contains('edit-btn') && todoId) {
      this.editTodo(todoId);
    } else if (target.classList.contains('filter-btn')) {
      const filter = target.dataset.filter;
      this.setState({ filter });
    } else if (target.classList.contains('add-btn')) {
      this.addTodo();
    }
  }

  handleChange(event) {
    if (event.target.classList.contains('todo-checkbox')) {
      const todoId = event.target.closest('.todo-item').dataset.id;
      this.toggleTodo(todoId);
    }
  }

  handleKeyPress(event) {
    if (event.key === 'Enter' && event.target.classList.contains('new-todo-input')) {
      this.addTodo();
    }
  }

  async addTodo() {
    const input = this.element.querySelector('.new-todo-input');
    const text = input.value.trim();
    
    if (!text) return;

    try {
      const newTodo = await this.#api.post('/todos', { text, completed: false });
      const todos = [...this.state.todos, newTodo];
      this.setState({ todos });
      input.value = '';
    } catch (error) {
      console.error('Failed to add todo:', error);
    }
  }

  async toggleTodo(id) {
    const todos = this.state.todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    );
    
    this.setState({ todos });

    try {
      const todo = todos.find(t => t.id === id);
      await this.#api.put(`/todos/${id}`, todo);
    } catch (error) {
      console.error('Failed to update todo:', error);
      // Revert on error
      this.loadTodos();
    }
  }

  async deleteTodo(id) {
    const todos = this.state.todos.filter(todo => todo.id !== id);
    this.setState({ todos });

    try {
      await this.#api.delete(`/todos/${id}`);
    } catch (error) {
      console.error('Failed to delete todo:', error);
      // Revert on error
      this.loadTodos();
    }
  }

  editTodo(id) {
    // Implementation for inline editing
    const todoItem = this.element.querySelector(`[data-id="${id}"]`);
    const textSpan = todoItem.querySelector('.todo-text');
    const currentText = textSpan.textContent;
    
    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentText;
    input.className = 'edit-input';
    
    textSpan.replaceWith(input);
    input.focus();
    input.select();

    const finishEdit = async () => {
      const newText = input.value.trim();
      if (newText && newText !== currentText) {
        try {
          const updatedTodo = await this.#api.put(`/todos/${id}`, {
            ...this.state.todos.find(t => t.id === id),
            text: newText
          });
          
          const todos = this.state.todos.map(todo =>
            todo.id === id ? updatedTodo : todo
          );
          this.setState({ todos });
        } catch (error) {
          console.error('Failed to update todo:', error);
        }
      } else {
        // Revert to original text
        textSpan.textContent = currentText;
        input.replaceWith(textSpan);
      }
    };

    input.addEventListener('blur', finishEdit);
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') finishEdit();
      if (e.key === 'Escape') {
        textSpan.textContent = currentText;
        input.replaceWith(textSpan);
      }
    });
  }

  getFilteredTodos(todos, filter) {
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  }
}

// =============================================================================
// 6. APPLICATION INITIALIZATION
// =============================================================================

export class App {
  #components = new Map();
  #api;
  #eventBus;
  #stateManager;

  constructor() {
    this.#api = new SecureApiClient('/api/v1');
    this.#eventBus = new EventBus();
    this.#stateManager = new StateManager({
      user: null,
      theme: 'light',
      notifications: true
    });
  }

  async init() {
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await this.#eventBus.waitForEvent('DOMContentLoaded');
      }

      await this.loadUserSettings();
      this.initializeComponents();
      this.setupGlobalEventListeners();
      this.setupPerformanceMonitoring();
      
      console.log('App initialized successfully');
    } catch (error) {
      console.error('Failed to initialize app:', error);
    }
  }

  async loadUserSettings() {
    try {
      const settings = await this.#api.get('/user/settings');
      this.#stateManager.setState(settings);
    } catch (error) {
      console.warn('Could not load user settings, using defaults');
    }
  }

  initializeComponents() {
    // Initialize TodoList component
    const todoContainer = document.querySelector('#todo-app');
    if (todoContainer) {
      const todoList = new TodoList('#todo-app', this.#api);
      this.#components.set('todoList', todoList);
    }
  }

  setupGlobalEventListeners() {
    // Handle theme changes
    this.#stateManager.subscribe('theme', (theme) => {
      document.documentElement.setAttribute('data-theme', theme);
    });

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            this.openSearch();
            break;
          case '/':
            event.preventDefault();
            this.toggleShortcutsHelp();
            break;
        }
      }
    });

    // Handle visibility changes for performance
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseNonEssentialOperations();
      } else {
        this.resumeOperations();
      }
    });
  }

  setupPerformanceMonitoring() {
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach(entry => {
        switch (entry.entryType) {
          case 'largest-contentful-paint':
            console.log('LCP:', entry.startTime);
            break;
          case 'first-input':
            console.log('FID:', entry.processingStart - entry.startTime);
            break;
          case 'layout-shift':
            if (!entry.hadRecentInput) {
              console.log('CLS:', entry.value);
            }
            break;
        }
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
  }

  openSearch() {
    // Implementation for global search
    console.log('Opening search...');
  }

  toggleShortcutsHelp() {
    // Implementation for shortcuts help
    console.log('Toggling shortcuts help...');
  }

  pauseNonEssentialOperations() {
    // Pause animations, polling, etc.
    console.log('Pausing non-essential operations...');
  }

  resumeOperations() {
    // Resume operations
    console.log('Resuming operations...');
  }

  destroy() {
    // Cleanup all components
    this.#components.forEach(component => {
      if (typeof component.destroy === 'function') {
        component.destroy();
      }
    });
    this.#components.clear();
  }
}

// =============================================================================
// 7. STARTUP
// =============================================================================

// Initialize app when DOM is ready
const app = new App();

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Handle page unload cleanup
window.addEventListener('beforeunload', () => {
  app.destroy();
});

// Export for module usage
export default app;
