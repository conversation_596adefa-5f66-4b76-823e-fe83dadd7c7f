# FastAPI: Testing

## Summary

Testing FastAPI applications is straightforward thanks to <PERSON><PERSON>'s `TestClient`, which is built on top of `httpx`. This allows you to write tests using `pytest` in a way that is familiar to those who have used the `requests` library.

## Key Concepts

### `TestClient`

-   The `TestClient` allows you to make requests to your FastAPI application without needing a running server. It interacts directly with the application.
-   To use it, you instantiate it by passing your FastAPI app object to it: `client = TestClient(app)`.
-   The client object has methods that correspond to HTTP verbs, such as `client.get()`, `client.post()`, etc.

### Writing Tests with `pytest`

-   Tests are written as standard Python functions with names starting with `test_`.
-   `pytest` automatically discovers and runs these test functions.
-   Inside the tests, you use the `TestClient` to make requests to your application's endpoints.
-   Standard `assert` statements are used to verify the response status code, JSON body, headers, etc.

### Test Structure

-   Tests can be placed in separate files (e.g., `test_main.py`) alongside your application code.
-   This helps in organizing your project, especially for larger applications.
-   You can import your FastAPI `app` object into your test files to create the `TestClient`.

### Passing Data in Tests

-   **Path and Query Parameters:** Include them directly in the URL string.
-   **JSON Body:** Pass a Python dictionary to the `json` parameter of the request method (e.g., `client.post(..., json=my_dict)`).
-   **Headers:** Pass a dictionary to the `headers` parameter.
-   **Cookies:** Pass a dictionary to the `cookies` parameter.
-   **Form Data:** Use the `data` parameter instead of `json`.

## Code Examples

### Basic Test

```python
# main.py
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
async def read_main():
    return {"msg": "Hello World"}
```

```python
# test_main.py
from fastapi.testclient import TestClient
from .main import app

client = TestClient(app)

def test_read_main():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"msg": "Hello World"}
```

### Testing with Headers and Error Conditions

```python
# test_main.py (extended)
def test_read_item_bad_token():
    response = client.get("/items/foo", headers={"X-Token": "hailhydra"})
    assert response.status_code == 400
    assert response.json() == {"detail": "Invalid X-Token header"}

def test_read_nonexistent_item():
    response = client.get("/items/baz", headers={"X-Token": "coneofsilence"})
    assert response.status_code == 404
    assert response.json() == {"detail": "Item not found"}

def test_create_existing_item():
    response = client.post(
        "/items/",
        headers={"X-Token": "coneofsilence"},
        json={
            "id": "foo",
            "title": "The Foo ID Stealers",
            "description": "There goes my stealer",
        },
    )
    assert response.status_code == 409
    assert response.json() == {"detail": "Item already exists"}
```

### Running Tests

To run the tests, you need to install `pytest` and `httpx`:

```bash
pip install pytest httpx
```

Then, from your project's root directory, simply run:

```bash
pytest
```
