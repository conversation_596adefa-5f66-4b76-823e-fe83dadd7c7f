# Generate PRP Command for Student Helper

## Overview
Generate a comprehensive Product Requirements Prompt (PRP) for Student Helper features with thorough research using Firecrawl MCP and Context7 MCP tools.

## Usage
Use this command to create detailed implementation blueprints that include all necessary context for successful feature development.

## Process

### 1. Research Phase (MANDATORY)
**CRITICAL**: Never skip the research phase. Use available MCP tools extensively.

#### Technology Research
- Use **Firecrawl MCP** to scrape 30-100 pages of official documentation
- Use **Context7 MCP** to get comprehensive library documentation  
- Store ALL research in `context-engineering/research/[technology]/` directories
- Create individual .md files for each successful scrape

#### Research Areas Required:
- **Backend Technologies**: Node.js, Python (FastAPI/Django), PHP <PERSON>
- **Frontend Technologies**: HTML5, CSS3, JavaScript ES6+, build tools
- **LangChain Framework**: Core concepts, agents, tools, memory management
- **Research APIs**: Firecrawl, BrightData, Tavily - complete documentation
- **Database Technologies**: PostgreSQL, MongoDB, Redis caching
- **Security Frameworks**: Authentication, authorization, data protection

### 2. Codebase Analysis
- Search existing codebase for similar patterns
- Identify conventions and architectural patterns
- Note testing approaches and validation strategies
- Check existing integrations and API patterns

### 3. Architecture Planning
Use comprehensive research to make informed decisions:
- Technology stack recommendations with justification
- Integration patterns and best practices
- Security considerations and implementations
- Performance optimization strategies

### 4. PRP Generation
Create detailed PRPs using `context-engineering/PRPs/templates/prp_base.md`:

#### Phase 1 PRP: Skeleton Implementation
- Detailed implementation comments
- Architecture setup and configuration
- Basic functionality with comprehensive TODO comments
- Testing framework setup

#### Phase 2 PRP: Production Implementation  
- Complete, production-ready code
- All features fully implemented
- Comprehensive error handling
- Security hardening
- Performance optimization
- Complete test coverage

## Quality Standards

### Research Requirements
- **Minimum 30-100 pages** of official documentation per technology
- **Official sources only** - no third-party tutorials or blogs
- **Organized storage** in technology-specific directories
- **Successful scrapes only** - retry failed scrapes until successful

### PRP Quality Criteria
- **Complete Context**: All necessary information included
- **Executable Validation**: All validation commands must work
- **Clear Implementation Path**: Step-by-step implementation guide
- **Security Focus**: Comprehensive security considerations
- **Performance Targets**: Specific performance requirements
- **Testing Strategy**: Complete testing approach

### Code Quality Standards
- **No Placeholder Code**: Everything must be fully functional
- **Production Ready**: Code ready for production deployment
- **Comprehensive Testing**: Unit, integration, and end-to-end tests
- **Security Hardened**: Following security best practices
- **Well Documented**: Clear documentation and comments

## Command Structure

```
/generate-prp [feature-file]
```

### Example Usage
```
/generate-prp context-engineering/INITIAL.md
```

## Output Files
- `context-engineering/PRPs/phase-1-[feature-name].md` - Skeleton implementation
- `context-engineering/PRPs/phase-2-[feature-name].md` - Production implementation

## Research Tools Integration

### Using Firecrawl MCP
```
Use Firecrawl to scrape official documentation:
- Technology documentation sites
- API reference documentation  
- Security guidelines and best practices
- Performance optimization guides
```

### Using Context7 MCP
```
Use Context7 to get comprehensive library docs:
- LangChain documentation and examples
- Framework-specific integration guides
- API client libraries and SDKs
- Testing and deployment documentation
```

## Validation Process

### Pre-Implementation Validation
- [ ] Research completeness check (30+ pages per technology)
- [ ] All documentation stored and organized
- [ ] Technology choices justified with research
- [ ] Security considerations documented
- [ ] Performance requirements defined

### Post-Generation Validation
- [ ] PRP includes all necessary context
- [ ] Implementation path is clear and detailed
- [ ] Validation commands are executable
- [ ] Code examples are accurate and complete
- [ ] Success criteria are measurable

## Success Metrics

### Confidence Score Target: 9/10
Achieve high confidence through:
- **Comprehensive Research**: Extensive official documentation
- **Clear Architecture**: Well-defined system design
- **Proven Patterns**: Using established best practices
- **Complete Context**: All information needed for implementation
- **Thorough Testing**: Comprehensive testing strategy

### Implementation Success Criteria
- **One-Pass Implementation**: Should work correctly on first attempt
- **No Placeholder Code**: All code fully functional
- **Security Compliant**: Meets security standards
- **Performance Targets**: Achieves performance requirements
- **Test Coverage**: 90%+ test coverage

## Notes
- **Student Helper Focus**: All PRPs specific to student productivity needs
- **API Flexibility**: Ensure research APIs are interchangeable
- **Incremental Development**: Build features step by step
- **Quality Over Speed**: Focus on creating high-quality, well-researched implementations
