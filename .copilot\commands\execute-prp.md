# Execute PRP Command for Student Helper

## Overview
Execute a comprehensive Product Requirements Prompt (PRP) to implement Student Helper features with full context and validation.

## Usage
Use this command to implement features from detailed PRPs created by the generate-prp command.

## Process

### 1. Context Loading
- Read the complete PRP file specified
- Load all referenced research documentation
- Review existing codebase patterns and conventions
- Understand integration requirements and dependencies

### 2. Implementation Planning
- Create detailed task breakdown from PRP
- Establish implementation order and dependencies
- Set up validation checkpoints throughout process
- Plan testing strategy for each component

### 3. Step-by-Step Execution
Follow the PRP implementation blueprint exactly:

#### Phase 1: Foundation Setup
- Create necessary directory structure
- Set up configuration files
- Establish testing framework
- Implement core architecture components

#### Phase 2: Core Implementation
- Implement primary functionality
- Add API integrations following research patterns
- Implement security measures
- Add comprehensive error handling

#### Phase 3: Testing & Validation
- Run all validation commands from PRP
- Execute unit tests with coverage targets
- Perform integration testing
- Validate security requirements

#### Phase 4: Documentation & Polish
- Update documentation
- Optimize performance
- Final security review
- User experience validation

### 4. Continuous Validation
Throughout implementation:
- Run validation commands after each major change
- Test functionality at each checkpoint
- Verify security requirements
- Check performance targets

## Implementation Standards

### Code Quality Requirements
- **Maximum 500 lines per file** - refactor if approaching limit
- **Comprehensive commenting** - explain complex logic and decisions
- **Consistent naming** - follow established project conventions
- **Error handling** - graceful handling of all error scenarios
- **Security first** - implement security measures from the start

### Testing Requirements
- **Unit tests** for all functions and classes
- **Integration tests** for API interactions
- **End-to-end tests** for user workflows
- **Security tests** for authentication and authorization
- **Performance tests** for response time requirements

### Documentation Requirements
- **Inline code documentation** with clear explanations
- **API documentation** for all endpoints
- **User documentation** for new features
- **Developer documentation** for future maintenance

## Validation Commands

### Code Quality Validation
```bash
# Linting and formatting (language-specific)
[appropriate linting commands]

# Type checking (if applicable)
[type checking commands]

# Security scanning
[security validation commands]
```

### Testing Validation
```bash
# Unit tests
[unit test execution commands]

# Integration tests  
[integration test execution commands]

# Coverage reporting
[coverage analysis commands]
```

### Performance Validation
```bash
# Performance testing
[performance test commands]

# Load testing (if applicable)
[load test commands]
```

## Command Structure

```
/execute-prp [prp-file-path]
```

### Example Usage
```
/execute-prp context-engineering/PRPs/phase-1-research-agent.md
/execute-prp context-engineering/PRPs/phase-2-research-agent.md
```

## Error Handling & Recovery

### Implementation Failures
- **Validation Failures**: Re-read PRP context and retry
- **Test Failures**: Debug and fix before proceeding
- **Integration Issues**: Refer to research documentation
- **Security Issues**: Review security requirements and re-implement

### Quality Assurance
- **Code Review**: Self-review against PRP requirements
- **Documentation Check**: Ensure all documentation is complete
- **Performance Verification**: Confirm performance targets are met
- **Security Validation**: Verify all security measures are implemented

## Success Criteria

### Functional Success
- [ ] All PRP requirements implemented
- [ ] All validation commands pass
- [ ] All tests pass with required coverage
- [ ] Performance targets achieved
- [ ] Security requirements met

### Quality Success  
- [ ] Code follows project standards
- [ ] Documentation is complete and accurate
- [ ] Error handling is comprehensive
- [ ] User experience is intuitive
- [ ] Code is maintainable and extensible

### Integration Success
- [ ] Seamlessly integrates with existing codebase
- [ ] API integrations work correctly
- [ ] Database operations function properly
- [ ] Authentication and authorization work
- [ ] Real-time features (if applicable) function correctly

## Monitoring & Feedback

### Implementation Progress
- Track completion of each PRP section
- Monitor test coverage improvements
- Verify performance metrics
- Check security compliance

### Quality Metrics
- Code complexity analysis
- Documentation completeness
- Test coverage percentages
- Performance benchmarks
- Security scan results

## Post-Implementation Tasks

### Documentation Updates
- Update `context-engineering/TASK.md` with completed tasks
- Update `context-engineering/PLANNING.md` if architecture changes
- Update `README.md` with new features and setup instructions
- Create or update user documentation

### Testing Integration
- Add new tests to CI/CD pipeline
- Update test coverage requirements
- Add performance benchmarks
- Include security tests in regular validation

### Deployment Preparation
- Verify deployment readiness
- Update deployment documentation
- Test in staging environment
- Prepare rollback procedures

## Notes

### Student Helper Specific Considerations
- **User Privacy**: Ensure student data privacy throughout
- **Performance**: Maintain sub-2-second response times
- **Accessibility**: Ensure features are accessible to all students
- **Mobile Responsiveness**: Test on multiple device types

### API Integration Focus
- **Interchangeable APIs**: Ensure research APIs can be swapped
- **Error Handling**: Graceful degradation when APIs fail
- **Rate Limiting**: Respect API rate limits and implement backoff
- **Caching**: Implement intelligent caching strategies

### Security Priorities
- **Input Validation**: Sanitize all user inputs
- **Authentication**: Secure user authentication throughout
- **Data Encryption**: Encrypt sensitive student data
- **API Security**: Secure external API integrations
