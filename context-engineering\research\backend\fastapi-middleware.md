# FastAPI: Middleware

## Summary

Middleware in FastAPI is a function that processes every request before it reaches a specific path operation and every response before it is sent back to the client. It allows for executing code globally on incoming requests and outgoing responses.

## Key Concepts

### Creating Middleware

- Middleware is created using the `@app.middleware("http")` decorator on an `async` function.
- The middleware function receives the `request` object and a `call_next` function.
- `call_next(request)` passes the request to the corresponding path operation and returns the response.
- Code can be executed before and after the `call_next` await, allowing modification of both the request and the response.

### Execution Order

- When multiple middlewares are added, they form a stack.
- The last middleware added is the outermost one and runs first for requests.
- The first middleware added is the innermost one and runs last for requests.
- The response flows through the middlewares in the reverse order.

### Common Use Cases

- Measuring and logging request processing time.
- Adding custom headers to responses.
- Handling authentication or authorization globally.
- Implementing CORS (Cross-Origin Resource Sharing).

## Code Example

### Adding a Process Time Header

This middleware calculates the time taken to process a request and adds it to a custom `X-Process-Time` header in the response.

```python
import time
from fastapi import FastAPI, Request

app = FastAPI()

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.perf_counter()
    response = await call_next(request)
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### Adding Middleware with `app.add_middleware()`

Besides the decorator, you can also use the `app.add_middleware()` method, which is useful for adding middleware from external libraries, like Starlette's `CORSMiddleware`.

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```
