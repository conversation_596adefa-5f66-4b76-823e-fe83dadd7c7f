---
applyTo: '**'
---

# Student Helper - Context Engineering Instructions

## 🔄 Project Awareness & Context & Research
- **Documentation is a source of truth** - Your knowledge is out of date, I will always give you the latest documentation before writing any files that use third party APIs - that information was freshly scraped and you should NOT use your own knowledge, but rather use the documentation as a source of absolute truth.
- **Always read `context-engineering/PLANNING.md`** at the start of a new conversation to understand the project's architecture, goals, style, and constraints.
- **Check `context-engineering/TASK.md`** before starting a new task. If the task isn't listed, add it with a brief description and today's date.
- **Use consistent naming conventions, file structure, and architecture patterns** as described in `context-engineering/PLANNING.md`.
- **MANDATORY RESEARCH FIRST** - Before coding ANYTHING, you must either:
  1. Refer to existing research in `context-engineering/research/` directory
  2. Conduct comprehensive research using Firecrawl MCP and Context7 MCP tools
  3. Store ALL research in separate .md files in `context-engineering/research/[technology]/` directories
- **Research Requirements**: Always scrape 30-100 pages of official documentation when researching new technologies
- **Stick to OFFICIAL DOCUMENTATION PAGES ONLY** - For all research ONLY use official documentation pages
- **Create comprehensive research documents** - Each successful scrape should be stored as individual .md files organized by technology
- **Refer to research before coding** - Before implementing any feature that uses external APIs or libraries, refer to the relevant directory inside `context-engineering/research/` directory
- **NO PLACEHOLDER CODE** - Never create placeholder code. Everything must be fully functional and production-ready
- **Incremental Development** - Build slowly and methodically, ensuring each component works before moving to the next

## 🎯 Student Helper App Context
- **Frontend**: Simple HTML, CSS, and JavaScript only
- **Backend**: Research-based decision (extensive research needed on best options)
- **Core Features**: Dashboard with calendar, task list, and intelligent research agent
- **Research Agent**: Must use interchangeable APIs (Firecrawl, BrightData, Tavily)
- **LangChain Integration**: Extensive research and documentation scraping required
- **Target Users**: Students needing intelligent research assistance and organization tools

## 🧱 Code Structure & Modularity
- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Research Agent Design**: Agents should be designed as intelligent human beings with decision-making capabilities, detailed research abilities using APIs, and reasoning-based problem solving
- **Organize code into clearly separated modules**, grouped by feature or responsibility:
  - `frontend/` - HTML, CSS, JavaScript files
  - `backend/` - Server-side logic (technology TBD after research)
  - `research-agent/` - Intelligent research agent components
  - `database/` - Database schemas and migrations
  - `api/` - API integration modules
- **Use clear, consistent imports** and maintain proper separation of concerns
- **Environment Configuration**: Use proper environment variable management

## 🔬 Research Agent Requirements
- **Intelligent Decision Making**: Research agents must use reasoning and AI decision-making, not programmatic solutions
- **API Flexibility**: Must support multiple research APIs that can be swapped easily
- **Data Storage**: Research results must be stored and accessible for future reference
- **Context Awareness**: Agents should understand the context of research requests
- **Quality Control**: Built-in mechanisms to verify and validate research quality

## 🧪 Testing & Reliability
- **Always create comprehensive tests for new features** (functions, classes, routes, etc)
- **Test Coverage**: Include unit tests, integration tests, and end-to-end tests where applicable
- **Research Agent Testing**: Special focus on testing API integrations and agent decision-making
- **Frontend Testing**: Test user interactions and responsive design
- **Backend Testing**: Test API endpoints, database operations, and business logic

## ✅ Task Completion & Documentation
- **Mark completed tasks in `context-engineering/TASK.md`** immediately after finishing them
- **Research Documentation**: All research must be documented in markdown files with clear organization
- **API Documentation**: Document all API integrations with examples and error handling
- **User Documentation**: Maintain clear documentation for end users
- **Developer Documentation**: Keep technical documentation up-to-date for future development

## 📎 Style & Conventions
- **Frontend**: Clean, modern HTML5, CSS3, and vanilla JavaScript
- **CSS Framework**: Research and decide on appropriate framework (Bootstrap, Tailwind, or custom)
- **JavaScript**: Modern ES6+ syntax, modular approach
- **Backend**: Technology to be determined through research phase
- **Database**: Research optimal database solution for student productivity app
- **API Design**: RESTful principles, clear error handling, proper status codes

## 🌐 Research API Integration
- **Multi-Provider Support**: Code must support multiple research API providers
- **Configuration-Driven**: Easy switching between providers via configuration
- **Error Handling**: Robust error handling for API failures and rate limiting
- **Caching**: Implement intelligent caching to reduce API calls and improve performance
- **Security**: Secure API key management and data handling

## 📚 Documentation & Explainability
- **Update `README.md`** when new features are added, dependencies change, or setup steps are modified
- **Research Documentation**: Maintain comprehensive research logs in `context-engineering/research/`
- **API Documentation**: Document all external API integrations
- **Architecture Documentation**: Keep system architecture diagrams and explanations current
- **Comment complex logic** with clear explanations of reasoning

## 🧠 AI Behavior Rules
- **RESEARCH MANDATORY**: Never code without first conducting or reviewing comprehensive research
- **Never assume missing context**: Always ask questions or conduct research if uncertain
- **Never hallucinate libraries or functions**: Only use verified packages and APIs based on research
- **Validate everything**: Always confirm file paths, module names, and API endpoints exist
- **Incremental progress**: Build features step by step, testing each component
- **Quality over speed**: Focus on creating high-quality, well-researched code rather than rushing

## 🔄 Context Engineering Workflow
1. **Read PLANNING.md and TASK.md** to understand current project state
2. **Review existing research** in `context-engineering/research/` directory
3. **Conduct additional research** if needed using Firecrawl MCP and Context7 MCP
4. **Document all research** in properly organized markdown files
5. **Create or update PRP** (Product Requirements Prompt) if implementing new features
6. **Implement incrementally** with testing at each stage
7. **Update documentation** and task tracking
8. **Validate against research** to ensure accuracy

## 🎨 Design System
- **Consistent UI/UX**: Develop and maintain a cohesive design system
- **Accessibility**: Ensure all components meet accessibility standards
- **Responsive Design**: Support for desktop, tablet, and mobile devices
- **User-Centered Design**: Focus on student needs and workflows
- **Performance**: Optimize for fast loading and smooth interactions

## 🔐 Security & Privacy
- **Data Protection**: Implement proper data encryption and storage practices
- **API Security**: Secure handling of research API keys and tokens
- **User Privacy**: Respect student privacy and data protection requirements
- **Input Validation**: Sanitize all user inputs and API responses
- **Error Handling**: Secure error messages that don't expose sensitive information