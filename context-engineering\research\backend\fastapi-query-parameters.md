Here is some relevant context from the web page https://fastapi.tiangolo.com/tutorial/query-params/:
...
# Query Parameters¶
...
## Defaults¶


As query parameters are not a fixed part of a path, they can be optional and can
have default values.


In the example above they have default values of `skip=0` and `limit=10`.


So, going to the URL:

 
```
 http://127.0.0.1:8000/items/

```

would be the same as going to:

 
```
 http://127.0.0.1:8000/items/?skip=0&limit=10

```

But if you go to, for example:

 
```
 http://127.0.0.1:8000/items/?skip=20

```

The parameter values in your function will be:


• `skip=20`: because you set it in the URL
• `limit=10`: because that was the default value


...
...
# Query Parameters¶
...
## Optional parameters¶


The same way, you can declare optional query parameters, by setting their
default to `None`:

  Python 3.10+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 
 @app.get("/items/{item_id}")
 async def read_item(item_id: str, q: str | None = None):
     if q:
         return {"item_id": item_id, "q": q}
     return {"item_id": item_id}

```
  🤓 Other versions and variants
In this case, the function parameter `q` will be optional, and will be `None` by default.


Check


Also notice that FastAPI is smart enough to notice that the path parameter `item_id` is a path parameter and `q` is not, so, it's a query parameter.


...
...
# Query Parameters¶
...
## Query parameter type conversion¶


You can also declare `bool` types, and they will be converted:

  Python 3.10+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 
 @app.get("/items/{item_id}")
 async def read_item(item_id: str, q: str | None = None, short: bool = False):
     item = {"item_id": item_id}
     if q:
         item.update({"q": q})
     if not short:
         item.update(
             {"description": "This is an amazing item that has a long description"}
         )
     return item

```
  🤓 Other versions and variants
In this case, if you go to:

 
```
 http://127.0.0.1:8000/items/foo?short=1

```

or

 
```
 http://127.0.0.1:8000/items/foo?short=True

```

or

 
```
 http://127.0.0.1:8000/items/foo?short=true

```

or
...
...
# Query Parameters¶
...
## Query parameter type conversion¶
...
```
 http://127.0.0.1:8000/items/foo?short=on

```

or

 
```
 http://127.0.0.1:8000/items/foo?short=yes

```

or any other case variation (uppercase, first letter in uppercase, etc), your
function will see the parameter `short` with a `bool` value of `True`. Otherwise as `False`.
...
...
# Query Parameters¶
...
## Multiple path and query parameters¶


You can declare multiple path parameters and query parameters at the same time, FastAPI knows which is which.


And you don't have to declare them in any specific order.


They will be detected by name:

  Python 3.10+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 
 @app.get("/users/{user_id}/items/{item_id}")
 async def read_user_item(
     user_id: int, item_id: str, q: str | None = None, short: bool = False
 ):
     item = {"item_id": item_id, "owner_id": user_id}
     if q:
         item.update({"q": q})
     if not short:
         item.update(
             {"description": "This is an amazing item that has a long description"}
         )
     return item

```
  🤓 Other versions and variants
...
...
# Query Parameters¶
...
## Required query parameters¶


When you declare a default value for non-path parameters (for now, we have only
seen query parameters), then it is not required.


If you don't want to add a specific value but just make it optional, set the
default as `None`.


But when you want to make a query parameter required, you can just not declare
any default value:

  Python 3.8+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 
 @app.get("/items/{item_id}")
 async def read_user_item(item_id: str, needy: str):
     item = {"item_id": item_id, "needy": needy}
     return item

```
  
Here the query parameter `needy` is a required query parameter of type `str`.


If you open in your browser a URL like:

 
```
 http://127.0.0.1:8000/items/foo-item

```

...without adding the required parameter `needy`, you will see an error like:

 
```
 {
   "detail": [
     {
 ...
...
# Query Parameters¶
...
## Required query parameters¶
...
       "type": "missing",
       "loc": [
         "query",
         "needy"
       ],
       "msg": "Field required",
       "input": null,
       "url": "https://errors.pydantic.dev/2.1/v/missing"
     }
   ]
 }

```

As `needy` is a required parameter, you would need to set it in the URL:

 
```
 http://127.0.0.1:8000/items/foo-item?needy=sooooneedy

```

...this would work:

 
```
 {
     "item_id": "foo-item",
     "needy": "sooooneedy"
 }

```

And of course, you can define some parameters as required, some as having a
default value, and some entirely optional:

  Python 3.10+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 
 @app.get("/items/{item_id}")
 async def read_user_item(
 ...
...
# Query Parameters¶
...
## Required query parameters¶
...
     item_id: str, needy: str, skip: int = 0, limit: int | None = None
 ):
     item = {"item_id": item_id, "needy": needy, "skip": skip, "limit": limit}
     return item

```
  🤓 Other versions and variants
In this case, there are 3 query parameters:


• `needy`, a required `str`.
• `skip`, an `int` with a default value of `0`.
• `limit`, an optional `int`.


Tip


You could also use `Enum`s the same way as with [Path Parameters ↪](https://fastapi.tiangolo.com/tutorial/path-params/#predefined-values).

[Image: Image]
...
...
# Query Parameters¶
...
## Required query parameters¶
...
 Back to top The FastAPI trademark is owned by [@tiangolo](https://tiangolo.com/) and is registered in the US and across other regionsMade with [Material for MkDocs](https://squidfunk.github.io/mkdocs-material/)[github.com](https://github.com/fastapi/fastapi)[discord.gg](https://discord.gg/VQjSZaeJmf)[twitter.com](https://twitter.com/fastapi)[www.linkedin.com](https://www.linkedin.com/company/fastapi)[tiangolo.com](https://tiangolo.com/) 
...
...
# Query Parameters¶
...
## Additional Links
- [en - English](https://fastapi.tiangolo.com/)
- [az - azərbaycan dili](https://fastapi.tiangolo.com/az/)
- [bn - বাংলা](https://fastapi.tiangolo.com/bn/)
- [de - Deutsch](https://fastapi.tiangolo.com/de/)
- [es - español](https://fastapi.tiangolo.com/es/)
- [fa - فارسی](https://fastapi.tiangolo.com/fa/)
- [fr - français](https://fastapi.tiangolo.com/fr/)
- [he - עברית](https://fastapi.tiangolo.com/he/)
- [hu - magyar](https://fastapi.tiangolo.com/hu/)
- [id - Bahasa Indonesia](https://fastapi.tiangolo.com/id/)
- [it - italiano](https://fastapi.tiangolo.com/it/)
- [ja - 日本語](https://fastapi.tiangolo.com/ja/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [ko - 한국어](https://fastapi.tiangolo.com/ko/)
- [nl - Nederlands](https://fastapi.tiangolo.com/nl/)
- [pl - Polski](https://fastapi.tiangolo.com/pl/)
- [pt - português](https://fastapi.tiangolo.com/pt/)
- [ru - русский язык](https://fastapi.tiangolo.com/ru/)
- [tr - Türkçe](https://fastapi.tiangolo.com/tr/)
- [uk - українська мова](https://fastapi.tiangolo.com/uk/)
- [ur - اردو](https://fastapi.tiangolo.com/ur/)
- [vi - Tiếng Việt](https://fastapi.tiangolo.com/vi/)
- [yo - Yorùbá](https://fastapi.tiangolo.com/yo/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [zh - 简体中文](https://fastapi.tiangolo.com/zh/)
- [zh-hant - 繁體中文](https://fastapi.tiangolo.com/zh-hant/)
- [😉](https://fastapi.tiangolo.com/em/)
- [Share](https://fastapi.tiangolo.com/tutorial/query-params/?q=) - Share
- [FastAPI](https://fastapi.tiangolo.com/) - FastAPI
- [fastapi/fastapi](https://github.com/fastapi/fastapi) - Go to repository
- [FastAPI](https://fastapi.tiangolo.com/)
- [Features](https://fastapi.tiangolo.com/features/)
- [Learn](https://fastapi.tiangolo.com/learn/)
- [Python Types Intro](https://fastapi.tiangolo.com/python-types/)
- [Concurrency and async / await](https://fastapi.tiangolo.com/async/)
- [Environment Variables](https://fastapi.tiangolo.com/environment-variables/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Virtual Environments](https://fastapi.tiangolo.com/virtual-environments/)
- [Tutorial - User Guide](https://fastapi.tiangolo.com/tutorial/)
- [First Steps](https://fastapi.tiangolo.com/tutorial/first-steps/)
- [Path Parameters](https://fastapi.tiangolo.com/tutorial/path-params/)
- [Defaults](https://fastapi.tiangolo.com/tutorial/query-params/#defaults)
- [Optional parameters](https://fastapi.tiangolo.com/tutorial/query-params/#optional-parameters)
- [Query parameter type conversion](https://fastapi.tiangolo.com/tutorial/query-params/#query-parameter-type-conversion)
- [Multiple path and query parameters](https://fastapi.tiangolo.com/tutorial/query-params/#multiple-path-and-query-parameters)
- [Required query parameters](https://fastapi.tiangolo.com/tutorial/query-params/#required-query-parameters)
- [Request Body](https://fastapi.tiangolo.com/tutorial/body/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Query Parameters and String Validations](https://fastapi.tiangolo.com/tutorial/query-params-str-validations/)
- [Path Parameters and Numeric Validations](https://fastapi.tiangolo.com/tutorial/path-params-numeric-validations/)
- [Query Parameter Models](https://fastapi.tiangolo.com/tutorial/query-param-models/)
- [Body - Multiple Parameters](https://fastapi.tiangolo.com/tutorial/body-multiple-params/)
- [Body - Fields](https://fastapi.tiangolo.com/tutorial/body-fields/)
- [Body - Nested Models](https://fastapi.tiangolo.com/tutorial/body-nested-models/)
- [Declare Request Example Data](https://fastapi.tiangolo.com/tutorial/schema-extra-example/)
- [Extra Data Types](https://fastapi.tiangolo.com/tutorial/extra-data-types/)
- [Cookie Parameters](https://fastapi.tiangolo.com/tutorial/cookie-params/)
- [Header Parameters](https://fastapi.tiangolo.com/tutorial/header-params/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Cookie Parameter Models](https://fastapi.tiangolo.com/tutorial/cookie-param-models/)
- [Header Parameter Models](https://fastapi.tiangolo.com/tutorial/header-param-models/)
- [Response Model - Return Type](https://fastapi.tiangolo.com/tutorial/response-model/)
- [Extra Models](https://fastapi.tiangolo.com/tutorial/extra-models/)
- [Response Status Code](https://fastapi.tiangolo.com/tutorial/response-status-code/)
- [Form Data](https://fastapi.tiangolo.com/tutorial/request-forms/)
- [Form Models](https://fastapi.tiangolo.com/tutorial/request-form-models/)
- [Request Files](https://fastapi.tiangolo.com/tutorial/request-files/)
- [Request Forms and Files](https://fastapi.tiangolo.com/tutorial/request-forms-and-files/)
- [Handling Errors](https://fastapi.tiangolo.com/tutorial/handling-errors/)
- [Path Operation Configuration](https://fastapi.tiangolo.com/tutorial/path-operation-configuration/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [JSON Compatible Encoder](https://fastapi.tiangolo.com/tutorial/encoder/)
- [Body - Updates](https://fastapi.tiangolo.com/tutorial/body-updates/)
- [Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/)
- [Classes as Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/classes-as-dependencies/)
- [Sub-dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/sub-dependencies/)
- [Dependencies in path operation decorators](https://fastapi.tiangolo.com/tutorial/dependencies/dependencies-in-path-operation-decorators/)
- [Global Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/global-dependencies/)
- [Dependencies with yield](https://fastapi.tiangolo.com/tutorial/dependencies/dependencies-with-yield/)
- [Security](https://fastapi.tiangolo.com/tutorial/security/)
- [Security - First Steps](https://fastapi.tiangolo.com/tutorial/security/first-steps/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Get Current User](https://fastapi.tiangolo.com/tutorial/security/get-current-user/)
- [Simple OAuth2 with Password and Bearer](https://fastapi.tiangolo.com/tutorial/security/simple-oauth2/)
- [OAuth2 with Password (and hashing), Bearer with JWT tokens](https://fastapi.tiangolo.com/tutorial/security/oauth2-jwt/)
- [Middleware](https://fastapi.tiangolo.com/tutorial/middleware/)
- [CORS (Cross-Origin Resource Sharing)](https://fastapi.tiangolo.com/tutorial/cors/)
- [SQL (Relational) Databases](https://fastapi.tiangolo.com/tutorial/sql-databases/)
- [Bigger Applications - Multiple Files](https://fastapi.tiangolo.com/tutorial/bigger-applications/)
- [Background Tasks](https://fastapi.tiangolo.com/tutorial/background-tasks/)
- [Metadata and Docs URLs](https://fastapi.tiangolo.com/tutorial/metadata/)
- [Static Files](https://fastapi.tiangolo.com/tutorial/static-files/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Testing](https://fastapi.tiangolo.com/tutorial/testing/)
- [Debugging](https://fastapi.tiangolo.com/tutorial/debugging/)
- [Advanced User Guide](https://fastapi.tiangolo.com/advanced/)
- [Path Operation Advanced Configuration](https://fastapi.tiangolo.com/advanced/path-operation-advanced-configuration/)
- [Additional Status Codes](https://fastapi.tiangolo.com/advanced/additional-status-codes/)
- [Return a Response Directly](https://fastapi.tiangolo.com/advanced/response-directly/)
- [Custom Response - HTML, Stream, File, others](https://fastapi.tiangolo.com/advanced/custom-response/)
- [Additional Responses in OpenAPI](https://fastapi.tiangolo.com/advanced/additional-responses/)
- [Response Cookies](https://fastapi.tiangolo.com/advanced/response-cookies/)
- [Response Headers](https://fastapi.tiangolo.com/advanced/response-headers/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Response - Change Status Code](https://fastapi.tiangolo.com/advanced/response-change-status-code/)
- [Advanced Dependencies](https://fastapi.tiangolo.com/advanced/advanced-dependencies/)
- [Advanced Security](https://fastapi.tiangolo.com/advanced/security/)
- [OAuth2 scopes](https://fastapi.tiangolo.com/advanced/security/oauth2-scopes/)
- [HTTP Basic Auth](https://fastapi.tiangolo.com/advanced/security/http-basic-auth/)
- [Using the Request Directly](https://fastapi.tiangolo.com/advanced/using-request-directly/)
- [Using Dataclasses](https://fastapi.tiangolo.com/advanced/dataclasses/)
- [Advanced Middleware](https://fastapi.tiangolo.com/advanced/middleware/)
- [Sub Applications - Mounts](https://fastapi.tiangolo.com/advanced/sub-applications/)
- [Behind a Proxy](https://fastapi.tiangolo.com/advanced/behind-a-proxy/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Templates](https://fastapi.tiangolo.com/advanced/templates/)
- [WebSockets](https://fastapi.tiangolo.com/advanced/websockets/)
- [Lifespan Events](https://fastapi.tiangolo.com/advanced/events/)
- [Testing WebSockets](https://fastapi.tiangolo.com/advanced/testing-websockets/)
- [Testing Events: startup - shutdown](https://fastapi.tiangolo.com/advanced/testing-events/)
- [Testing Dependencies with Overrides](https://fastapi.tiangolo.com/advanced/testing-dependencies/)
- [Async Tests](https://fastapi.tiangolo.com/advanced/async-tests/)
- [Settings and Environment Variables](https://fastapi.tiangolo.com/advanced/settings/)
- [OpenAPI Callbacks](https://fastapi.tiangolo.com/advanced/openapi-callbacks/)
- [OpenAPI Webhooks](https://fastapi.tiangolo.com/advanced/openapi-webhooks/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Including WSGI - Flask, Django, others](https://fastapi.tiangolo.com/advanced/wsgi/)
- [Generate Clients](https://fastapi.tiangolo.com/advanced/generate-clients/)
- [FastAPI CLI](https://fastapi.tiangolo.com/fastapi-cli/)
- [Deployment](https://fastapi.tiangolo.com/deployment/)
- [About FastAPI versions](https://fastapi.tiangolo.com/deployment/versions/)
- [About HTTPS](https://fastapi.tiangolo.com/deployment/https/)
- [Run a Server Manually](https://fastapi.tiangolo.com/deployment/manually/)
- [Deployments Concepts](https://fastapi.tiangolo.com/deployment/concepts/)
- [Deploy FastAPI on Cloud Providers](https://fastapi.tiangolo.com/deployment/cloud/)
- [Server Workers - Uvicorn with Workers](https://fastapi.tiangolo.com/deployment/server-workers/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [FastAPI in Containers - Docker](https://fastapi.tiangolo.com/deployment/docker/)
- [How To - Recipes](https://fastapi.tiangolo.com/how-to/)
- [General - How To - Recipes](https://fastapi.tiangolo.com/how-to/general/)
- [GraphQL](https://fastapi.tiangolo.com/how-to/graphql/)
- [Custom Request and APIRoute class](https://fastapi.tiangolo.com/how-to/custom-request-and-route/)
- [Conditional OpenAPI](https://fastapi.tiangolo.com/how-to/conditional-openapi/)
- [Extending OpenAPI](https://fastapi.tiangolo.com/how-to/extending-openapi/)
- [Separate OpenAPI Schemas for Input and Output or Not](https://fastapi.tiangolo.com/how-to/separate-openapi-schemas/)
- [Custom Docs UI Static Assets (Self-Hosting)](https://fastapi.tiangolo.com/how-to/custom-docs-ui-assets/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Configure Swagger UI](https://fastapi.tiangolo.com/how-to/configure-swagger-ui/)
- [Testing a Database](https://fastapi.tiangolo.com/how-to/testing-database/)
- [Reference](https://fastapi.tiangolo.com/reference/)
- [FastAPI class](https://fastapi.tiangolo.com/reference/fastapi/)
- [Request Parameters](https://fastapi.tiangolo.com/reference/parameters/)
- [Status Codes](https://fastapi.tiangolo.com/reference/status/)
- [UploadFile class](https://fastapi.tiangolo.com/reference/uploadfile/)
- [Exceptions - HTTPException and WebSocketException](https://fastapi.tiangolo.com/reference/exceptions/)
- [Dependencies - Depends() and Security()](https://fastapi.tiangolo.com/reference/dependencies/)
- [APIRouter class](https://fastapi.tiangolo.com/reference/apirouter/)
- [Background Tasks - BackgroundTasks](https://fastapi.tiangolo.com/reference/background/)
- [Request class](https://fastapi.tiangolo.com/reference/request/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [WebSockets](https://fastapi.tiangolo.com/reference/websockets/)
- [HTTPConnection class](https://fastapi.tiangolo.com/reference/httpconnection/)
- [Response class](https://fastapi.tiangolo.com/reference/response/)
- [Custom Response Classes - File, HTML, Redirect, Streaming, etc.](https://fastapi.tiangolo.com/reference/responses/)
- [Middleware](https://fastapi.tiangolo.com/reference/middleware/)
- [OpenAPI](https://fastapi.tiangolo.com/reference/openapi/)
- [OpenAPI docs](https://fastapi.tiangolo.com/reference/openapi/docs/)
- [OpenAPI models](https://fastapi.tiangolo.com/reference/openapi/models/)
- [Security Tools](https://fastapi.tiangolo.com/reference/security/)
- [Encoders - jsonable_encoder](https://fastapi.tiangolo.com/reference/encoders/)
- [Static Files - StaticFiles](https://fastapi.tiangolo.com/reference/staticfiles/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Templating - Jinja2Templates](https://fastapi.tiangolo.com/reference/templating/)
- [Test Client - TestClient](https://fastapi.tiangolo.com/reference/testclient/)
- [FastAPI People](https://fastapi.tiangolo.com/fastapi-people/)
- [Resources](https://fastapi.tiangolo.com/resources/)
- [Help FastAPI - Get Help](https://fastapi.tiangolo.com/help-fastapi/)
- [Development - Contributing](https://fastapi.tiangolo.com/contributing/)
- [Full Stack FastAPI Template](https://fastapi.tiangolo.com/project-generation/)
- [External Links and Articles](https://fastapi.tiangolo.com/external-links/)
- [FastAPI and friends newsletter](https://fastapi.tiangolo.com/newsletter/)
- [Repository Management Tasks](https://fastapi.tiangolo.com/management-tasks/)
- [About](https://fastapi.tiangolo.com/about/)
...
...
# Query Parameters¶
...
## Additional Links
...
- [Alternatives, Inspiration and Comparisons](https://fastapi.tiangolo.com/alternatives/)
- [History, Design and Future](https://fastapi.tiangolo.com/history-design-future/)
- [Benchmarks](https://fastapi.tiangolo.com/benchmarks/)
- [Repository Management](https://fastapi.tiangolo.com/management/)
- [Release Notes](https://fastapi.tiangolo.com/release-notes/)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Learn](https://fastapi.tiangolo.com/learn/)
- [Tutorial - User Guide](https://fastapi.tiangolo.com/tutorial/)
- [Previous: Path Parameters](https://fastapi.tiangolo.com/tutorial/path-params/)
- [Next: Request Body](https://fastapi.tiangolo.com/tutorial/body/)
...
# Query Parameters¶


When you declare other function parameters that are not part of the path
parameters, they are automatically interpreted as "query" parameters.

  Python 3.8+ 
```
 from fastapi import FastAPI
 
 app = FastAPI()
 
 fake_items_db = [{"item_name": "Foo"}, {"item_name": "Bar"}, {"item_name": "Baz"}]
 
 
 @app.get("/items/")
 async def read_item(skip: int = 0, limit: int = 10):
     return fake_items_db[skip : skip + limit]

```
  
The query is the set of key-value pairs that go after the `?` in a URL, separated by `&` characters.


For example, in the URL:

 
```
 http://127.0.0.1:8000/items/?skip=0&limit=10

```

...the query parameters are:


• `skip`: with a value of `0`
• `limit`: with a value of `10`


As they are part of the URL, they are "naturally" strings.
...
...
# Query Parameters¶
...
But when you declare them with Python types (in the example above, as `int`), they are converted to that type and validated against it.


All the same process that applied for path parameters also applies for query
parameters:


• Editor support (obviously)
• Data "parsing"
• Data validation
• Automatic documentation


## Defaults¶
...
## Optional parameters¶
...
## Query parameter type conversion¶
...
## Multiple path and query parameters¶
...
## Required query parameters¶n...
## Additional Links
...
  Skip to content  [ Follow @fastapi on Twitter to stay updated](https://twitter.com/fastapi) [sponsor](https://zuplo.link/fastapi-web)   
# Query Parameters¶
...
